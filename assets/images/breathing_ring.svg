<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter id="blur" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="5" />
    </filter>
    <radialGradient id="glow" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
      <stop offset="60%" stop-color="#87CEEB" stop-opacity="0.3" />
      <stop offset="80%" stop-color="#87CEEB" stop-opacity="0.2" />
      <stop offset="95%" stop-color="#ADD8E6" stop-opacity="0.1" />
      <stop offset="100%" stop-color="#FFFFFF" stop-opacity="0" />
    </radialGradient>
  </defs>
  <circle cx="100" cy="100" r="70" fill="none" stroke="url(#glow)" stroke-width="40" filter="url(#blur)"/>
  <circle cx="100" cy="100" r="80" fill="none" stroke="#AFEEEE" stroke-width="2" stroke-opacity="0.8"/>
  <circle cx="100" cy="100" r="75" fill="none" stroke="#E0FFFF" stroke-width="1.5" stroke-opacity="0.7"/>
</svg>