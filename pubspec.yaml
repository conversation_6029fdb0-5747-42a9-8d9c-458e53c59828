name: gs_fit_flutter
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.2.3 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  webview_flutter:
    git:
      url: "https://gitcode.com/openharmony-sig/flutter_packages.git"
      path: "packages/webview_flutter/webview_flutter"
  encrypt: ^5.0.3
  shared_preferences:
    git:
      url: "https://gitcode.com/openharmony-sig/flutter_packages.git"
      path: "packages/shared_preferences/shared_preferences"
  # 图片选择
  image_picker:
    git:
      url: "https://gitcode.com/openharmony-sig/flutter_packages.git"
      path: "packages/image_picker/image_picker"
  audioplayers:
    git:
      url: https://gitcode.com/openharmony-sig/flutter_audioplayers.git
      path: packages/audioplayers
  flutter_local_notifications:
    git:
      url: https://gitcode.com/openharmony-sig/fluttertpc_flutter_local_notifications.git
      path: flutter_local_notifications  
  cupertino_icons: ^1.0.2
  # 网络
  dio: 5.3.2
  # getx 框架
  get: 4.6.6
  plugin_platform_interface: ^2.1.8
  # 刷新
  pull_to_refresh: ^2.0.0
  flutter_easyloading: ^3.0.5
  json_annotation: 4.9.0
  intl: ^0.20.2
  fl_chart: ^0.63.0
  path: ^1.8.3
  crypto: ^3.0.6
  qr_flutter: ^4.1.0
  mobile_scanner: ^6.0.10
  table_calendar: ^3.2.0

dependency_overrides:
  sqflite:
    git:
      #      url: "https://gitcode.com/openharmony-sig/flutter_sqflite.git"
      url: https://gitee.com/openharmony-sig/flutter_sqflite.git
      path: sqflite/
      ref: "br_v2.3.3+1_ohos"

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  build_runner: 2.4.11
  json_serializable: 6.8.0



# 设备启动图
flutter_native_splash:
  color: "#ffffff"
  image: "assets/icons/launcher_android.png"

# app 图标
flutter_icons:
  android: true
  ios: true
  image_path: "assets/icons/launcher_ios.png"
  # image_path_ios: "assets/icons/launcher_ios.png"
  image_path_android: "assets/icons/launcher_android.png"
  adaptive_icon_background: "#ffffff"

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  assets:
    - assets/images/
    - assets/images/ic_health_sport_box.png
    - assets/images/ic_health_sport_ckal.png
    - assets/images/ic_health_sport_heart_rate.png
    - assets/images/ic_health_sport_record.png
    - assets/images/ic_health_sport_sleep.png
    - assets/images/ic_health_sport_sugar.png
    - assets/images/ic_indoor_type_cycling.png
    - assets/images/ic_indoor_type_free.png
    - assets/images/ic_indoor_type_run.png
    - assets/images/ic_indoor_type_swimming.png
    - assets/images/ic_indoor_type_walk.png
    - assets/images/ic_indoor_type_yuga.png
    - assets/images/my/
    - assets/images/discover/
    - assets/images/discover/background/
    - assets/images/discover/ic_focus/
    - assets/images/discover/sleep/
    - assets/images/nav/
    - assets/images/nav/nav_device_select.png
    - assets/images/nav/nav_device_unselect.png
    - assets/images/nav/nav_find_select.png
    - assets/images/nav/nav_find_unselect.png
    - assets/images/nav/nav_health_select.png
    - assets/images/nav/nav_health_unselect.png
    - assets/images/nav/nav_mine_select.png
    - assets/images/nav/nav_mine_unselect.png
    - assets/images/nav/nav_sport_select.png
    - assets/images/nav/nav_sport_unselect.png
    - assets/images/equipment/
    - assets/images/equipment/icon_equipment_30.png
    - assets/images/other/
    - assets/raw/


  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  fonts:
    - family: Montserrat-Bold
      fonts:
        - asset: assets/fonts/Montserrat/Montserrat-Bold.ttf
    - family: Montserrat-Light
      fonts:
        - asset: assets/fonts/Montserrat/Montserrat-Light.ttf
    - family: Montserrat-Medium
      fonts:
        - asset: assets/fonts/Montserrat/Montserrat-Medium.ttf
    - family: Montserrat-Regular
      fonts:
        - asset: assets/fonts/Montserrat/Montserrat-Regular.ttf

  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
