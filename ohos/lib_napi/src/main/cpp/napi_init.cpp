#ifdef __APPLE__
#include "napi_mock.h"
#else
#include "napi/native_api.h"
#endif
#include "opus_player.h"
#include <map>
#include <memory>

/**
 * 测试函数：验证NAPI模块基本功能
 */
static napi_value TestNapi(napi_env env, napi_callback_info info) {
    napi_value result;
    napi_create_string_utf8(env, "NAPI module loaded successfully!", NAPI_AUTO_LENGTH, &result);
    return result;
}

// 全局播放器管理
static std::map<int32_t, std::unique_ptr<OpusPlayer>> g_players;
static int32_t g_nextPlayerId = 1;

/**
 * 创建Opus播放器
 * 参数: sampleRate(number), channels(number)
 * 返回: playerId(number) 或 -1(失败)
 */
static napi_value CreateOpusPlayer(napi_env env, napi_callback_info info) {
    size_t argc = 2;
    napi_value args[2] = {nullptr};
    napi_get_cb_info(env, info, &argc, args, nullptr, nullptr);

    // 获取参数
    int32_t sampleRate = 48000;
    int32_t channels = 2;

    if (argc >= 1) {
        napi_get_value_int32(env, args[0], &sampleRate);
    }
    if (argc >= 2) {
        napi_get_value_int32(env, args[1], &channels);
    }

    // 创建播放器
    auto player = std::make_unique<OpusPlayer>();
    if (!player->initialize(sampleRate, channels)) {
        napi_value result;
        napi_create_int32(env, -1, &result);
        return result;
    }

    // 分配ID并保存
    int32_t playerId = g_nextPlayerId++;
    g_players[playerId] = std::move(player);

    napi_value result;
    napi_create_int32(env, playerId, &result);
    return result;
}

/**
 * 加载Opus音频数据
 * 参数: playerId(number), opusData(ArrayBuffer)
 * 返回: success(boolean)
 */
static napi_value LoadOpusData(napi_env env, napi_callback_info info) {
    size_t argc = 2;
    napi_value args[2] = {nullptr};
    napi_get_cb_info(env, info, &argc, args, nullptr, nullptr);

    if (argc < 2) {
        napi_value result;
        napi_get_boolean(env, false, &result);
        return result;
    }

    // 获取播放器ID
    int32_t playerId;
    napi_get_value_int32(env, args[0], &playerId);

    auto it = g_players.find(playerId);
    if (it == g_players.end()) {
        napi_value result;
        napi_get_boolean(env, false, &result);
        return result;
    }

    // 获取ArrayBuffer数据
    void* data;
    size_t dataSize;
    napi_get_arraybuffer_info(env, args[1], &data, &dataSize);

    // 加载数据
    bool success = it->second->loadOpusData(
        static_cast<const unsigned char*>(data), dataSize);

    napi_value result;
    napi_get_boolean(env, success, &result);
    return result;
}

/**
 * 开始播放
 * 参数: playerId(number)
 * 返回: success(boolean)
 */
static napi_value PlayOpus(napi_env env, napi_callback_info info) {
    size_t argc = 1;
    napi_value args[1] = {nullptr};
    napi_get_cb_info(env, info, &argc, args, nullptr, nullptr);

    if (argc < 1) {
        napi_value result;
        napi_get_boolean(env, false, &result);
        return result;
    }

    int32_t playerId;
    napi_get_value_int32(env, args[0], &playerId);

    auto it = g_players.find(playerId);
    if (it == g_players.end()) {
        napi_value result;
        napi_get_boolean(env, false, &result);
        return result;
    }

    bool success = it->second->play();

    napi_value result;
    napi_get_boolean(env, success, &result);
    return result;
}

/**
 * 暂停播放
 * 参数: playerId(number)
 * 返回: success(boolean)
 */
static napi_value PauseOpus(napi_env env, napi_callback_info info) {
    size_t argc = 1;
    napi_value args[1] = {nullptr};
    napi_get_cb_info(env, info, &argc, args, nullptr, nullptr);

    if (argc < 1) {
        napi_value result;
        napi_get_boolean(env, false, &result);
        return result;
    }

    int32_t playerId;
    napi_get_value_int32(env, args[0], &playerId);

    auto it = g_players.find(playerId);
    if (it == g_players.end()) {
        napi_value result;
        napi_get_boolean(env, false, &result);
        return result;
    }

    bool success = it->second->pause();

    napi_value result;
    napi_get_boolean(env, success, &result);
    return result;
}

/**
 * 停止播放
 * 参数: playerId(number)
 * 返回: success(boolean)
 */
static napi_value StopOpus(napi_env env, napi_callback_info info) {
    size_t argc = 1;
    napi_value args[1] = {nullptr};
    napi_get_cb_info(env, info, &argc, args, nullptr, nullptr);

    if (argc < 1) {
        napi_value result;
        napi_get_boolean(env, false, &result);
        return result;
    }

    int32_t playerId;
    napi_get_value_int32(env, args[0], &playerId);

    auto it = g_players.find(playerId);
    if (it == g_players.end()) {
        napi_value result;
        napi_get_boolean(env, false, &result);
        return result;
    }

    bool success = it->second->stop();

    napi_value result;
    napi_get_boolean(env, success, &result);
    return result;
}

/**
 * 设置音量
 * 参数: playerId(number), volume(number 0.0-1.0)
 * 返回: success(boolean)
 */
static napi_value SetVolume(napi_env env, napi_callback_info info) {
    size_t argc = 2;
    napi_value args[2] = {nullptr};
    napi_get_cb_info(env, info, &argc, args, nullptr, nullptr);

    if (argc < 2) {
        napi_value result;
        napi_get_boolean(env, false, &result);
        return result;
    }

    int32_t playerId;
    double volume;
    napi_get_value_int32(env, args[0], &playerId);
    napi_get_value_double(env, args[1], &volume);

    auto it = g_players.find(playerId);
    if (it == g_players.end()) {
        napi_value result;
        napi_get_boolean(env, false, &result);
        return result;
    }

    bool success = it->second->setVolume(static_cast<float>(volume));

    napi_value result;
    napi_get_boolean(env, success, &result);
    return result;
}

/**
 * 获取播放状态
 * 参数: playerId(number)
 * 返回: state(number) 0=停止, 1=播放, 2=暂停, -1=错误
 */
static napi_value GetPlayState(napi_env env, napi_callback_info info) {
    size_t argc = 1;
    napi_value args[1] = {nullptr};
    napi_get_cb_info(env, info, &argc, args, nullptr, nullptr);

    if (argc < 1) {
        napi_value result;
        napi_create_int32(env, -1, &result);
        return result;
    }

    int32_t playerId;
    napi_get_value_int32(env, args[0], &playerId);

    auto it = g_players.find(playerId);
    if (it == g_players.end()) {
        napi_value result;
        napi_create_int32(env, -1, &result);
        return result;
    }

    int32_t state = static_cast<int32_t>(it->second->getState());

    napi_value result;
    napi_create_int32(env, state, &result);
    return result;
}

/**
 * 获取当前播放位置
 * 参数: playerId(number)
 * 返回: position(number) 毫秒
 */
static napi_value GetCurrentPosition(napi_env env, napi_callback_info info) {
    size_t argc = 1;
    napi_value args[1] = {nullptr};
    napi_get_cb_info(env, info, &argc, args, nullptr, nullptr);

    if (argc < 1) {
        napi_value result;
        napi_create_int64(env, 0, &result);
        return result;
    }

    int32_t playerId;
    napi_get_value_int32(env, args[0], &playerId);

    auto it = g_players.find(playerId);
    if (it == g_players.end()) {
        napi_value result;
        napi_create_int64(env, 0, &result);
        return result;
    }

    int64_t position = it->second->getCurrentPosition();

    napi_value result;
    napi_create_int64(env, position, &result);
    return result;
}

/**
 * 获取总时长
 * 参数: playerId(number)
 * 返回: duration(number) 毫秒
 */
static napi_value GetDuration(napi_env env, napi_callback_info info) {
    size_t argc = 1;
    napi_value args[1] = {nullptr};
    napi_get_cb_info(env, info, &argc, args, nullptr, nullptr);

    if (argc < 1) {
        napi_value result;
        napi_create_int64(env, 0, &result);
        return result;
    }

    int32_t playerId;
    napi_get_value_int32(env, args[0], &playerId);

    auto it = g_players.find(playerId);
    if (it == g_players.end()) {
        napi_value result;
        napi_create_int64(env, 0, &result);
        return result;
    }

    int64_t duration = it->second->getDuration();

    napi_value result;
    napi_create_int64(env, duration, &result);
    return result;
}

/**
 * 释放播放器
 * 参数: playerId(number)
 * 返回: success(boolean)
 */
static napi_value ReleaseOpusPlayer(napi_env env, napi_callback_info info) {
    size_t argc = 1;
    napi_value args[1] = {nullptr};
    napi_get_cb_info(env, info, &argc, args, nullptr, nullptr);

    if (argc < 1) {
        napi_value result;
        napi_get_boolean(env, false, &result);
        return result;
    }

    int32_t playerId;
    napi_get_value_int32(env, args[0], &playerId);

    auto it = g_players.find(playerId);
    if (it == g_players.end()) {
        napi_value result;
        napi_get_boolean(env, false, &result);
        return result;
    }

    it->second->release();
    g_players.erase(it);

    napi_value result;
    napi_get_boolean(env, true, &result);
    return result;
}

EXTERN_C_START
static napi_value Init(napi_env env, napi_value exports)
{
    napi_property_descriptor desc[] = {
        { "testNapi", nullptr, TestNapi, nullptr, nullptr, nullptr, napi_default, nullptr },
        { "createOpusPlayer", nullptr, CreateOpusPlayer, nullptr, nullptr, nullptr, napi_default, nullptr },
        { "loadOpusData", nullptr, LoadOpusData, nullptr, nullptr, nullptr, napi_default, nullptr },
        { "playOpus", nullptr, PlayOpus, nullptr, nullptr, nullptr, napi_default, nullptr },
        { "pauseOpus", nullptr, PauseOpus, nullptr, nullptr, nullptr, napi_default, nullptr },
        { "stopOpus", nullptr, StopOpus, nullptr, nullptr, nullptr, napi_default, nullptr },
        { "setVolume", nullptr, SetVolume, nullptr, nullptr, nullptr, napi_default, nullptr },
        { "getPlayState", nullptr, GetPlayState, nullptr, nullptr, nullptr, napi_default, nullptr },
        { "getCurrentPosition", nullptr, GetCurrentPosition, nullptr, nullptr, nullptr, napi_default, nullptr },
        { "getDuration", nullptr, GetDuration, nullptr, nullptr, nullptr, napi_default, nullptr },
        { "releaseOpusPlayer", nullptr, ReleaseOpusPlayer, nullptr, nullptr, nullptr, napi_default, nullptr }
    };
    napi_define_properties(env, exports, sizeof(desc) / sizeof(desc[0]), desc);
    return exports;
}
EXTERN_C_END

static napi_module demoModule = {
    .nm_version = 1,
    .nm_flags = 0,
    .nm_filename = nullptr,
    .nm_register_func = Init,
    .nm_modname = "lib_napi",
    .nm_priv = ((void*)0),
    .reserved = { 0 },
};

extern "C" __attribute__((constructor)) void RegisterLib_napiModule(void)
{
    napi_module_register(&demoModule);
}
