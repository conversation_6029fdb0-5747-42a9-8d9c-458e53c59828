#include "opus_player.h"
#include <iostream>
#include <cstring>
#include <algorithm>
#include <chrono>
#include <hilog/log.h>

#define LOG_DOMAIN 0x3200
#define LOG_TAG "OpusPlayer"

OpusPlayer::OpusPlayer()
    : opus_decoder_(nullptr)
    , sample_rate_(48000)
    , channels_(2)
    , frame_size_(960)
    , current_state_(PlayState::STOPPED)
    , current_position_(0)
    , total_duration_(0)
    , volume_(1.0f)
    , should_stop_(false)
    , is_initialized_(false) {
    log("INFO", "OpusPlayer created");
}

OpusPlayer::~OpusPlayer() {
    release();
    log("INFO", "OpusPlayer destroyed");
}

bool OpusPlayer::initialize(int sample_rate, int channels) {
    if (is_initialized_) {
        log("INFO", "OpusPlayer already initialized");
        return true;
    }

    // 验证参数
    if (sample_rate != 8000 && sample_rate != 12000 && 
        sample_rate != 16000 && sample_rate != 24000 && 
        sample_rate != 48000) {
        log("ERROR", "Unsupported sample rate: " + std::to_string(sample_rate));
        return false;
    }

    if (channels < 1 || channels > 2) {
        log("ERROR", "Unsupported channel count: " + std::to_string(channels));
        return false;
    }

    sample_rate_ = sample_rate;
    channels_ = channels;
    frame_size_ = sample_rate / 50; // 20ms frame

    // 创建Opus解码器
    int error;
    opus_decoder_ = opus_decoder_create(sample_rate_, channels_, &error);
    if (error != OPUS_OK || !opus_decoder_) {
        log("ERROR", "Failed to create Opus decoder: " + std::string(opus_strerror(error)));
        return false;
    }

    // 重置状态
    resetState();
    is_initialized_ = true;

    log("INFO", "OpusPlayer initialized - " + 
        std::to_string(sample_rate_) + "Hz, " + 
        std::to_string(channels_) + " channels");
    
    return true;
}

bool OpusPlayer::loadOpusData(const unsigned char* opus_data, size_t data_size) {
    if (!is_initialized_) {
        log("ERROR", "OpusPlayer not initialized");
        return false;
    }

    if (!opus_data || data_size == 0) {
        log("ERROR", "Invalid opus data");
        return false;
    }

    // 停止当前播放
    stop();

    // 清空现有数据
    opus_packets_.clear();
    clearPCMQueue();

    // 解析Opus文件数据
    if (!parseOpusFile(opus_data, data_size)) {
        log("ERROR", "Failed to parse opus file");
        return false;
    }

    // 计算总时长
    calculateDuration();

    log("INFO", "Opus data loaded - " + 
        std::to_string(opus_packets_.size()) + " packets, " +
        std::to_string(total_duration_) + "ms duration");

    return true;
}

bool OpusPlayer::play() {
    if (!is_initialized_) {
        log("ERROR", "OpusPlayer not initialized");
        return false;
    }

    if (opus_packets_.empty()) {
        log("ERROR", "No opus data loaded");
        return false;
    }

    if (current_state_ == PlayState::PLAYING) {
        log("INFO", "Already playing");
        return true;
    }

    // 从暂停状态恢复或开始新的播放
    should_stop_ = false;
    current_state_ = PlayState::PLAYING;

    // 启动播放线程
    if (play_thread_.joinable()) {
        play_thread_.join();
    }
    play_thread_ = std::thread(&OpusPlayer::playThreadFunction, this);

    log("INFO", "Playback started");
    return true;
}

bool OpusPlayer::pause() {
    if (current_state_ != PlayState::PLAYING) {
        return false;
    }

    current_state_ = PlayState::PAUSED;
    log("INFO", "Playback paused");
    return true;
}

bool OpusPlayer::stop() {
    if (current_state_ == PlayState::STOPPED) {
        return true;
    }

    should_stop_ = true;
    current_state_ = PlayState::STOPPED;
    
    // 通知播放线程停止
    queue_condition_.notify_all();
    
    // 等待播放线程结束
    if (play_thread_.joinable()) {
        play_thread_.join();
    }

    // 重置播放位置
    current_position_ = 0;
    clearPCMQueue();

    log("INFO", "Playback stopped");
    return true;
}

bool OpusPlayer::setVolume(float volume) {
    volume_ = std::max(0.0f, std::min(1.0f, volume));
    log("INFO", "Volume set to " + std::to_string(volume_));
    return true;
}

bool OpusPlayer::seekTo(int64_t position_ms) {
    if (!is_initialized_) {
        return false;
    }

    // 限制在有效范围内
    int64_t duration = total_duration_.load();
    position_ms = std::max(0LL, std::min(position_ms, duration));
    current_position_ = position_ms;

    // 清空PCM队列，重新开始解码
    clearPCMQueue();

    log("INFO", "Seeked to " + std::to_string(position_ms) + "ms");
    return true;
}

PlayState OpusPlayer::getState() const {
    return current_state_;
}

int64_t OpusPlayer::getCurrentPosition() const {
    return current_position_;
}

int64_t OpusPlayer::getDuration() const {
    return total_duration_;
}

float OpusPlayer::getVolume() const {
    return volume_;
}

void OpusPlayer::release() {
    stop();
    
    if (opus_decoder_) {
        opus_decoder_destroy(opus_decoder_);
        opus_decoder_ = nullptr;
    }
    
    opus_packets_.clear();
    clearPCMQueue();
    is_initialized_ = false;
    
    log("INFO", "OpusPlayer released");
}

bool OpusPlayer::isInitialized() const {
    return is_initialized_;
}

void OpusPlayer::log(const std::string& level, const std::string& message) {
    std::string full_message = "[" + level + "] " + message;
    
    if (level == "ERROR") {
        OH_LOG_ERROR(LOG_APP, "%{public}s", full_message.c_str());
    } else if (level == "DEBUG") {
        OH_LOG_DEBUG(LOG_APP, "%{public}s", full_message.c_str());
    } else {
        OH_LOG_INFO(LOG_APP, "%{public}s", full_message.c_str());
    }
}

void OpusPlayer::resetState() {
    current_state_ = PlayState::STOPPED;
    current_position_ = 0;
    total_duration_ = 0;
    should_stop_ = false;
    clearPCMQueue();
}

void OpusPlayer::clearPCMQueue() {
    std::lock_guard<std::mutex> lock(queue_mutex_);
    while (!pcm_queue_.empty()) {
        pcm_queue_.pop();
    }
}

void OpusPlayer::setError(const std::string& error_msg) {
    current_state_ = PlayState::ERROR;
    log("ERROR", error_msg);
}

bool OpusPlayer::parseOpusFile(const unsigned char* data, size_t size) {
    // 检查是否是Ogg格式
    if (size >= 4 && memcmp(data, "OggS", 4) == 0) {
        return parseOggOpusFile(data, size);
    }

    // 尝试解析为原始Opus数据
    return parseRawOpusData(data, size);
}

bool OpusPlayer::parseOggOpusFile(const unsigned char* data, size_t size) {
    // 简化的Ogg解析实现
    // 在实际应用中，应该使用完整的Ogg解析库

    const unsigned char* ptr = data;
    size_t remaining = size;

    while (remaining > 27) { // Ogg页头最小长度
        // 检查Ogg页标识
        if (memcmp(ptr, "OggS", 4) != 0) {
            break;
        }

        // 跳过页头
        ptr += 26;
        remaining -= 26;

        if (remaining == 0) break;

        // 读取段表长度
        uint8_t segments = *ptr++;
        remaining--;

        if (remaining < segments) break;

        // 计算页数据长度
        size_t page_size = 0;
        for (int i = 0; i < segments; i++) {
            page_size += ptr[i];
        }

        ptr += segments;
        remaining -= segments;

        if (remaining < page_size) break;

        // 检查是否是Opus数据包
        if (page_size > 8) {
            OpusPacket packet;
            packet.data.assign(ptr, ptr + page_size);
            packet.timestamp = opus_packets_.size() * 20; // 假设20ms每包
            packet.duration_ms = 20;
            opus_packets_.push_back(packet);
        }

        ptr += page_size;
        remaining -= page_size;
    }

    return !opus_packets_.empty();
}

bool OpusPlayer::parseRawOpusData(const unsigned char* data, size_t size) {
    // 简化的原始Opus数据解析
    // 假设数据是连续的Opus帧，每帧前有2字节长度信息

    const unsigned char* ptr = data;
    size_t remaining = size;

    while (remaining > 2) {
        // 读取帧长度（小端序）
        uint16_t frame_length = ptr[0] | (ptr[1] << 8);
        ptr += 2;
        remaining -= 2;

        if (frame_length == 0 || frame_length > remaining) {
            break;
        }

        OpusPacket packet;
        packet.data.assign(ptr, ptr + frame_length);
        packet.timestamp = opus_packets_.size() * 20; // 假设20ms每包
        packet.duration_ms = 20;
        opus_packets_.push_back(packet);

        ptr += frame_length;
        remaining -= frame_length;
    }

    // 如果没有找到有效的帧结构，尝试将整个数据作为单个包
    if (opus_packets_.empty() && size > 0) {
        OpusPacket packet;
        packet.data.assign(data, data + size);
        packet.timestamp = 0;
        packet.duration_ms = 1000; // 假设1秒
        opus_packets_.push_back(packet);
    }

    return !opus_packets_.empty();
}

int OpusPlayer::decodeOpusPacket(const OpusPacket& packet, PCMBuffer& pcm_buffer) {
    if (!opus_decoder_) {
        return -1;
    }

    // 准备输出缓冲区
    pcm_buffer.samples.resize(frame_size_ * channels_);
    pcm_buffer.channels = channels_;
    pcm_buffer.sample_rate = sample_rate_;
    pcm_buffer.timestamp = packet.timestamp;

    // 解码Opus数据
    int samples = opus_decode_float(
        opus_decoder_,
        packet.data.data(),
        static_cast<opus_int32>(packet.data.size()),
        pcm_buffer.samples.data(),
        frame_size_,
        0
    );

    if (samples < 0) {
        log("ERROR", "Opus decode failed: " + std::string(opus_strerror(samples)));
        return -1;
    }

    // 调整缓冲区大小
    pcm_buffer.samples.resize(samples * channels_);

    // 应用音量
    float vol = volume_.load();
    if (vol != 1.0f) {
        for (float& sample : pcm_buffer.samples) {
            sample *= vol;
        }
    }

    return samples;
}

void OpusPlayer::calculateDuration() {
    int64_t total_ms = 0;
    for (const auto& packet : opus_packets_) {
        total_ms += packet.duration_ms;
    }
    total_duration_ = total_ms;
}

void OpusPlayer::playThreadFunction() {
    log("INFO", "Play thread started");

    size_t packet_index = 0;
    auto start_time = std::chrono::steady_clock::now();

    while (!should_stop_ && packet_index < opus_packets_.size()) {
        // 检查播放状态
        if (current_state_ == PlayState::PAUSED) {
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            continue;
        }

        if (current_state_ != PlayState::PLAYING) {
            break;
        }

        // 解码当前数据包
        const OpusPacket& packet = opus_packets_[packet_index];
        PCMBuffer pcm_buffer;

        int samples = decodeOpusPacket(packet, pcm_buffer);
        if (samples > 0) {
            // 更新播放位置
            current_position_ = packet.timestamp;

            // 模拟音频播放延迟
            // 在实际实现中，这里应该将PCM数据发送到音频输出设备
            auto frame_duration = std::chrono::milliseconds(packet.duration_ms);
            std::this_thread::sleep_for(frame_duration);

            log("DEBUG", "Played packet " + std::to_string(packet_index) +
                ", position: " + std::to_string(current_position_) + "ms");
        } else {
            log("ERROR", "Failed to decode packet " + std::to_string(packet_index));
        }

        packet_index++;
    }

    // 播放完成
    if (packet_index >= opus_packets_.size() && !should_stop_) {
        current_state_ = PlayState::STOPPED;
        current_position_ = total_duration_;
        log("INFO", "Playback completed");
    }

    log("INFO", "Play thread ended");
}

void OpusPlayer::decodeThreadFunction() {
    // 预解码线程实现（可选）
    // 在实际应用中可以用于提前解码数据包以减少播放延迟
    log("INFO", "Decode thread function called");
}
