#ifndef AUDIO_DECODER_H
#define AUDIO_DECODER_H

#include <opus.h>
#include <vector>
#include <memory>
#include <string>

/**
 * 音频解码器基类
 * 提供通用的音频解码接口
 */
class AudioDecoder {
public:
    virtual ~AudioDecoder() = default;
    
    /**
     * 初始化解码器
     * @param sample_rate 采样率
     * @param channels 声道数
     * @return 成功返回true
     */
    virtual bool initialize(int sample_rate, int channels) = 0;
    
    /**
     * 解码音频数据
     * @param input_data 输入的编码数据
     * @param input_size 输入数据大小
     * @param output_data 输出的PCM数据
     * @param max_output_size 最大输出大小
     * @return 实际解码的样本数，失败返回-1
     */
    virtual int decode(const unsigned char* input_data, int input_size,
                      float* output_data, int max_output_size) = 0;
    
    /**
     * 重置解码器状态
     */
    virtual void reset() = 0;
    
    /**
     * 释放解码器资源
     */
    virtual void release() = 0;
    
    /**
     * 获取采样率
     */
    virtual int getSampleRate() const = 0;
    
    /**
     * 获取声道数
     */
    virtual int getChannels() const = 0;
};

/**
 * Opus音频解码器实现
 */
class OpusAudioDecoder : public AudioDecoder {
public:
    OpusAudioDecoder();
    ~OpusAudioDecoder() override;
    
    bool initialize(int sample_rate, int channels) override;
    int decode(const unsigned char* input_data, int input_size,
              float* output_data, int max_output_size) override;
    void reset() override;
    void release() override;
    int getSampleRate() const override;
    int getChannels() const override;
    
    /**
     * 设置解码器选项
     * @param request 选项类型
     * @param value 选项值
     * @return 成功返回true
     */
    bool setOption(int request, int value);
    
    /**
     * 获取解码器选项
     * @param request 选项类型
     * @param value 输出选项值
     * @return 成功返回true
     */
    bool getOption(int request, int* value);
    
    /**
     * 获取最后的错误信息
     */
    std::string getLastError() const;

private:
    OpusDecoder* decoder_;
    int sample_rate_;
    int channels_;
    int frame_size_;
    std::string last_error_;
    bool initialized_;
    
    /**
     * 设置错误信息
     */
    void setError(const std::string& error);
    
    /**
     * 验证参数有效性
     */
    bool validateParameters(int sample_rate, int channels);
};

/**
 * 音频解码器工厂类
 */
class AudioDecoderFactory {
public:
    enum class CodecType {
        OPUS,
        // 可以扩展支持其他编解码器
        // AAC,
        // MP3,
        // FLAC
    };
    
    /**
     * 创建音频解码器
     * @param type 编解码器类型
     * @return 解码器实例，失败返回nullptr
     */
    static std::unique_ptr<AudioDecoder> createDecoder(CodecType type);
    
    /**
     * 检查是否支持指定的编解码器
     * @param type 编解码器类型
     * @return 支持返回true
     */
    static bool isSupported(CodecType type);
    
    /**
     * 获取支持的编解码器列表
     */
    static std::vector<CodecType> getSupportedCodecs();
};

#endif // AUDIO_DECODER_H
