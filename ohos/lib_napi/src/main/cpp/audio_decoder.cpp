#include "audio_decoder.h"
#include <algorithm>
#include <iostream>

// 模拟HarmonyOS日志系统（用于开发环境）
#ifdef __APPLE__
#define OH_LOG_INFO(domain, format, ...) printf("[INFO] " format "\n", ##__VA_ARGS__)
#define OH_LOG_ERROR(domain, format, ...) printf("[ERROR] " format "\n", ##__VA_ARGS__)
#define OH_LOG_DEBUG(domain, format, ...) printf("[DEBUG] " format "\n", ##__VA_ARGS__)
#define LOG_APP 0
#else
#include <hilog/log.h>
#define LOG_DOMAIN 0x3200
#define LOG_TAG "AudioDecoder"
#endif

// OpusAudioDecoder 实现

OpusAudioDecoder::OpusAudioDecoder()
    : decoder_(nullptr)
    , sample_rate_(48000)
    , channels_(2)
    , frame_size_(960)
    , initialized_(false) {
}

OpusAudioDecoder::~OpusAudioDecoder() {
    release();
}

bool OpusAudioDecoder::initialize(int sample_rate, int channels) {
    if (initialized_) {
        OH_LOG_INFO(LOG_APP, "OpusAudioDecoder already initialized");
        return true;
    }
    
    if (!validateParameters(sample_rate, channels)) {
        return false;
    }
    
    sample_rate_ = sample_rate;
    channels_ = channels;
    frame_size_ = sample_rate / 50; // 20ms frame
    
    // 创建Opus解码器
    int error;
    decoder_ = opus_decoder_create(sample_rate_, channels_, &error);
    if (error != OPUS_OK || !decoder_) {
        setError("Failed to create Opus decoder: " + std::string(opus_strerror(error)));
        return false;
    }
    
    initialized_ = true;
#ifdef __APPLE__
    printf("[INFO] OpusAudioDecoder initialized - %dHz, %d channels\n", sample_rate_, channels_);
#else
    OH_LOG_INFO(LOG_APP, "OpusAudioDecoder initialized - %{public}dHz, %{public}d channels",
                sample_rate_, channels_);
#endif
    
    return true;
}

int OpusAudioDecoder::decode(const unsigned char* input_data, int input_size,
                            float* output_data, int max_output_size) {
    if (!initialized_ || !decoder_) {
        setError("Decoder not initialized");
        return -1;
    }
    
    if (!input_data || input_size <= 0 || !output_data || max_output_size <= 0) {
        setError("Invalid parameters");
        return -1;
    }
    
    // 计算最大可解码的样本数
    int max_samples = max_output_size / channels_;
    max_samples = std::min(max_samples, frame_size_);
    
    // 解码Opus数据
    int samples = opus_decode_float(
        decoder_,
        input_data,
        input_size,
        output_data,
        max_samples,
        0
    );
    
    if (samples < 0) {
        setError("Opus decode failed: " + std::string(opus_strerror(samples)));
        return -1;
    }
    
    return samples;
}

void OpusAudioDecoder::reset() {
    if (decoder_) {
        opus_decoder_ctl(decoder_, OPUS_RESET_STATE);
        OH_LOG_INFO(LOG_APP, "OpusAudioDecoder reset");
    }
}

void OpusAudioDecoder::release() {
    if (decoder_) {
        opus_decoder_destroy(decoder_);
        decoder_ = nullptr;
    }
    
    initialized_ = false;
    OH_LOG_INFO(LOG_APP, "OpusAudioDecoder released");
}

int OpusAudioDecoder::getSampleRate() const {
    return sample_rate_;
}

int OpusAudioDecoder::getChannels() const {
    return channels_;
}

bool OpusAudioDecoder::setOption(int request, int value) {
    if (!decoder_) {
        setError("Decoder not initialized");
        return false;
    }
    
    int result = opus_decoder_ctl(decoder_, request, value);
    if (result != OPUS_OK) {
        setError("Failed to set option: " + std::string(opus_strerror(result)));
        return false;
    }
    
    return true;
}

bool OpusAudioDecoder::getOption(int request, int* value) {
    if (!decoder_ || !value) {
        setError("Invalid parameters");
        return false;
    }
    
    int result = opus_decoder_ctl(decoder_, request, value);
    if (result != OPUS_OK) {
        setError("Failed to get option: " + std::string(opus_strerror(result)));
        return false;
    }
    
    return true;
}

std::string OpusAudioDecoder::getLastError() const {
    return last_error_;
}

void OpusAudioDecoder::setError(const std::string& error) {
    last_error_ = error;
#ifdef __APPLE__
    printf("[ERROR] %s\n", error.c_str());
#else
    OH_LOG_ERROR(LOG_APP, "%{public}s", error.c_str());
#endif
}

bool OpusAudioDecoder::validateParameters(int sample_rate, int channels) {
    // 验证采样率
    if (sample_rate != 8000 && sample_rate != 12000 && 
        sample_rate != 16000 && sample_rate != 24000 && 
        sample_rate != 48000) {
        setError("Unsupported sample rate: " + std::to_string(sample_rate));
        return false;
    }
    
    // 验证声道数
    if (channels < 1 || channels > 2) {
        setError("Unsupported channel count: " + std::to_string(channels));
        return false;
    }
    
    return true;
}

// AudioDecoderFactory 实现

std::unique_ptr<AudioDecoder> AudioDecoderFactory::createDecoder(CodecType type) {
    switch (type) {
        case CodecType::OPUS:
            return std::make_unique<OpusAudioDecoder>();
        default:
            OH_LOG_ERROR(LOG_APP, "Unsupported codec type");
            return nullptr;
    }
}

bool AudioDecoderFactory::isSupported(CodecType type) {
    switch (type) {
        case CodecType::OPUS:
            return true;
        default:
            return false;
    }
}

std::vector<AudioDecoderFactory::CodecType> AudioDecoderFactory::getSupportedCodecs() {
    return {
        CodecType::OPUS
        // 可以添加其他支持的编解码器
    };
}
