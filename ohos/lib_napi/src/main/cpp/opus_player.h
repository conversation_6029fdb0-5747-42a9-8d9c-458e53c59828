#ifndef OPUS_PLAYER_H
#define OPUS_PLAYER_H

#include <opus.h>
#include <vector>
#include <memory>
#include <atomic>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <queue>

/**
 * 播放状态枚举
 */
enum class PlayState {
    STOPPED = 0,
    PLAYING = 1,
    PAUSED = 2,
    ERROR = -1
};

/**
 * Opus音频数据包结构
 */
struct OpusPacket {
    std::vector<unsigned char> data;
    int64_t timestamp;
    int duration_ms;
};

/**
 * PCM音频缓冲区
 */
struct PCMBuffer {
    std::vector<float> samples;
    int channels;
    int sample_rate;
    int64_t timestamp;
};

/**
 * Opus播放器类
 * 使用opus-1.5.2库进行解码，支持完整的Opus音频播放功能
 */
class OpusPlayer {
public:
    OpusPlayer();
    ~OpusPlayer();

    /**
     * 初始化播放器
     * @param sample_rate 采样率 (8000, 12000, 16000, 24000, 48000)
     * @param channels 声道数 (1=单声道, 2=立体声)
     * @return 成功返回true
     */
    bool initialize(int sample_rate, int channels);

    /**
     * 加载Opus音频数据
     * @param opus_data Opus编码数据
     * @param data_size 数据大小
     * @return 成功返回true
     */
    bool loadOpusData(const unsigned char* opus_data, size_t data_size);

    /**
     * 开始播放
     * @return 成功返回true
     */
    bool play();

    /**
     * 暂停播放
     * @return 成功返回true
     */
    bool pause();

    /**
     * 停止播放
     * @return 成功返回true
     */
    bool stop();

    /**
     * 设置音量
     * @param volume 音量值 (0.0 - 1.0)
     * @return 成功返回true
     */
    bool setVolume(float volume);

    /**
     * 跳转到指定位置
     * @param position_ms 位置(毫秒)
     * @return 成功返回true
     */
    bool seekTo(int64_t position_ms);

    /**
     * 获取当前播放状态
     * @return 播放状态
     */
    PlayState getState() const;

    /**
     * 获取当前播放位置
     * @return 位置(毫秒)
     */
    int64_t getCurrentPosition() const;

    /**
     * 获取总时长
     * @return 时长(毫秒)
     */
    int64_t getDuration() const;

    /**
     * 获取当前音量
     * @return 音量值 (0.0 - 1.0)
     */
    float getVolume() const;

    /**
     * 释放播放器资源
     */
    void release();

    /**
     * 检查播放器是否已初始化
     * @return 已初始化返回true
     */
    bool isInitialized() const;

private:
    // Opus解码器
    OpusDecoder* opus_decoder_;
    
    // 音频参数
    int sample_rate_;
    int channels_;
    int frame_size_;
    
    // 播放状态
    std::atomic<PlayState> current_state_;
    std::atomic<int64_t> current_position_;
    std::atomic<int64_t> total_duration_;
    std::atomic<float> volume_;
    
    // 音频数据
    std::vector<OpusPacket> opus_packets_;
    std::queue<PCMBuffer> pcm_queue_;
    std::mutex queue_mutex_;
    std::condition_variable queue_condition_;
    
    // 播放线程
    std::thread play_thread_;
    std::atomic<bool> should_stop_;
    std::atomic<bool> is_initialized_;
    
    // 私有方法
    
    /**
     * 解析Opus文件数据
     * @param data 文件数据
     * @param size 数据大小
     * @return 成功返回true
     */
    bool parseOpusFile(const unsigned char* data, size_t size);

    /**
     * 解析Ogg格式的Opus文件
     * @param data 文件数据
     * @param size 数据大小
     * @return 成功返回true
     */
    bool parseOggOpusFile(const unsigned char* data, size_t size);

    /**
     * 解析原始Opus数据
     * @param data 文件数据
     * @param size 数据大小
     * @return 成功返回true
     */
    bool parseRawOpusData(const unsigned char* data, size_t size);
    
    /**
     * 解码Opus数据包
     * @param packet Opus数据包
     * @param pcm_buffer 输出PCM缓冲区
     * @return 解码的样本数，失败返回-1
     */
    int decodeOpusPacket(const OpusPacket& packet, PCMBuffer& pcm_buffer);
    
    /**
     * 播放线程函数
     */
    void playThreadFunction();
    
    /**
     * 解码线程函数
     */
    void decodeThreadFunction();
    
    /**
     * 计算总时长
     */
    void calculateDuration();
    
    /**
     * 重置播放器状态
     */
    void resetState();
    
    /**
     * 清空PCM队列
     */
    void clearPCMQueue();
    
    /**
     * 设置错误状态
     * @param error_msg 错误信息
     */
    void setError(const std::string& error_msg);
    
    /**
     * 日志输出
     * @param level 日志级别 (INFO, ERROR, DEBUG)
     * @param message 日志信息
     */
    void log(const std::string& level, const std::string& message);
};

#endif // OPUS_PLAYER_H
