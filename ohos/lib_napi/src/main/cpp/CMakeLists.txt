# the minimum version of CMake.

# cmake_minimum_required(VERSION 3.1)

# 正确的最小版本声明
cmake_minimum_required(VERSION 3.5)

# 显式设置策略版本（解决兼容性问题）
if(NOT POLICY_VERSION_MINIMUM)
    set(POLICY_VERSION_MINIMUM 3.5)
endif()
cmake_policy(VERSION ${POLICY_VERSION_MINIMUM})

# cmake_minimum_required(VERSION 3.5.0)
project(lib_napi)

set(NATIVERENDER_ROOT_PATH ${CMAKE_CURRENT_SOURCE_DIR})
set(OPUS_ROOT_PATH ${CMAKE_CURRENT_SOURCE_DIR}/../../../opus-1.5.2)

if(DEFINED PACKAGE_FIND_FILE)
    include(${PACKAGE_FIND_FILE})
endif()

# Include directories
include_directories(${NATIVERENDER_ROOT_PATH}
                    ${NATIVERENDER_ROOT_PATH}/include
                    ${OPUS_ROOT_PATH}/include
                    ${OPUS_ROOT_PATH}/arm64-v8a-build)

# 使用预编译的opus库
add_library(opus STATIC IMPORTED)
set_target_properties(opus PROPERTIES
    IMPORTED_LOCATION ${OPUS_ROOT_PATH}/arm64-v8a-build/libopus.a
)

# Main NAPI library
add_library(lib_napi SHARED
    napi_init.cpp
    audio_decoder.cpp
    opus_player.cpp
)

# 设置编译选项
target_compile_definitions(lib_napi PRIVATE
    OPUS_BUILD
    HAVE_LRINT
    HAVE_LRINTF
    VAR_ARRAYS
)

# 链接库
target_link_libraries(lib_napi PUBLIC
    libace_napi.z.so
    opus
)