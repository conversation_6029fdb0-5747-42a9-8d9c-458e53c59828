# the minimum version of CMake.
cmake_minimum_required(VERSION 3.5)

# 显式设置策略版本（解决兼容性问题）
if(NOT POLICY_VERSION_MINIMUM)
    set(POLICY_VERSION_MINIMUM 3.5)
endif()
cmake_policy(VERSION ${POLICY_VERSION_MINIMUM})

project(lib_napi)

set(NATIVERENDER_ROOT_PATH ${CMAKE_CURRENT_SOURCE_DIR})
set(OPUS_ROOT_PATH ${CMAKE_CURRENT_SOURCE_DIR}/../../../opus-1.5.2)

if(DEFINED PACKAGE_FIND_FILE)
    include(${PACKAGE_FIND_FILE})
endif()

# Include directories
include_directories(${NATIVERENDER_ROOT_PATH}
                    ${NATIVERENDER_ROOT_PATH}/include
                    ${OPUS_ROOT_PATH}/include
                    ${OPUS_ROOT_PATH}/src
                    ${OPUS_ROOT_PATH}/celt
                    ${OPUS_ROOT_PATH}/silk
                    ${OPUS_ROOT_PATH}/silk/fixed
                    ${OPUS_ROOT_PATH}/silk/float)

# 核心Opus源文件
set(OPUS_CORE_SOURCES
    ${OPUS_ROOT_PATH}/src/opus.c
    ${OPUS_ROOT_PATH}/src/opus_decoder.c
    ${OPUS_ROOT_PATH}/src/opus_encoder.c
    ${OPUS_ROOT_PATH}/src/opus_multistream.c
    ${OPUS_ROOT_PATH}/src/opus_multistream_encoder.c
    ${OPUS_ROOT_PATH}/src/opus_multistream_decoder.c
    ${OPUS_ROOT_PATH}/src/repacketizer.c
    ${OPUS_ROOT_PATH}/celt/bands.c
    ${OPUS_ROOT_PATH}/celt/celt.c
    ${OPUS_ROOT_PATH}/celt/celt_encoder.c
    ${OPUS_ROOT_PATH}/celt/celt_decoder.c
    ${OPUS_ROOT_PATH}/celt/cwrs.c
    ${OPUS_ROOT_PATH}/celt/entcode.c
    ${OPUS_ROOT_PATH}/celt/entdec.c
    ${OPUS_ROOT_PATH}/celt/entenc.c
    ${OPUS_ROOT_PATH}/celt/kiss_fft.c
    ${OPUS_ROOT_PATH}/celt/laplace.c
    ${OPUS_ROOT_PATH}/celt/mathops.c
    ${OPUS_ROOT_PATH}/celt/mdct.c
    ${OPUS_ROOT_PATH}/celt/modes.c
    ${OPUS_ROOT_PATH}/celt/pitch.c
    ${OPUS_ROOT_PATH}/celt/celt_lpc.c
    ${OPUS_ROOT_PATH}/celt/quant_bands.c
    ${OPUS_ROOT_PATH}/celt/rate.c
    ${OPUS_ROOT_PATH}/celt/vq.c
    ${OPUS_ROOT_PATH}/silk/CNG.c
    ${OPUS_ROOT_PATH}/silk/code_signs.c
    ${OPUS_ROOT_PATH}/silk/init_decoder.c
    ${OPUS_ROOT_PATH}/silk/decode_core.c
    ${OPUS_ROOT_PATH}/silk/decode_frame.c
    ${OPUS_ROOT_PATH}/silk/decode_parameters.c
    ${OPUS_ROOT_PATH}/silk/decode_indices.c
    ${OPUS_ROOT_PATH}/silk/decode_pulses.c
    ${OPUS_ROOT_PATH}/silk/decoder_set_fs.c
    ${OPUS_ROOT_PATH}/silk/dec_API.c
    ${OPUS_ROOT_PATH}/silk/enc_API.c
    ${OPUS_ROOT_PATH}/silk/encode_indices.c
    ${OPUS_ROOT_PATH}/silk/encode_pulses.c
    ${OPUS_ROOT_PATH}/silk/gain_quant.c
    ${OPUS_ROOT_PATH}/silk/interpolate.c
    ${OPUS_ROOT_PATH}/silk/LP_variable_cutoff.c
    ${OPUS_ROOT_PATH}/silk/NLSF_decode.c
    ${OPUS_ROOT_PATH}/silk/NSQ.c
    ${OPUS_ROOT_PATH}/silk/NSQ_del_dec.c
    ${OPUS_ROOT_PATH}/silk/PLC.c
    ${OPUS_ROOT_PATH}/silk/shell_coder.c
    ${OPUS_ROOT_PATH}/silk/tables_gain.c
    ${OPUS_ROOT_PATH}/silk/tables_LTP.c
    ${OPUS_ROOT_PATH}/silk/tables_NLSF_CB_NB_MB.c
    ${OPUS_ROOT_PATH}/silk/tables_NLSF_CB_WB.c
    ${OPUS_ROOT_PATH}/silk/tables_other.c
    ${OPUS_ROOT_PATH}/silk/tables_pitch_lag.c
    ${OPUS_ROOT_PATH}/silk/tables_pulses_per_block.c
    ${OPUS_ROOT_PATH}/silk/VAD.c
    ${OPUS_ROOT_PATH}/silk/control_audio_bandwidth.c
    ${OPUS_ROOT_PATH}/silk/quant_LTP_gains.c
    ${OPUS_ROOT_PATH}/silk/VQ_WMat_EC.c
    ${OPUS_ROOT_PATH}/silk/HP_variable_cutoff.c
    ${OPUS_ROOT_PATH}/silk/NLSF_encode.c
    ${OPUS_ROOT_PATH}/silk/NLSF_VQ.c
    ${OPUS_ROOT_PATH}/silk/NLSF_unpack.c
    ${OPUS_ROOT_PATH}/silk/NLSF_del_dec_quant.c
    ${OPUS_ROOT_PATH}/silk/process_NLSFs.c
    ${OPUS_ROOT_PATH}/silk/stereo_LR_to_MS.c
    ${OPUS_ROOT_PATH}/silk/stereo_MS_to_LR.c
    ${OPUS_ROOT_PATH}/silk/check_control_input.c
    ${OPUS_ROOT_PATH}/silk/control_SNR.c
    ${OPUS_ROOT_PATH}/silk/init_encoder.c
    ${OPUS_ROOT_PATH}/silk/control_codec.c
    ${OPUS_ROOT_PATH}/silk/A2NLSF.c
    ${OPUS_ROOT_PATH}/silk/ana_filt_bank_1.c
    ${OPUS_ROOT_PATH}/silk/biquad_alt.c
    ${OPUS_ROOT_PATH}/silk/bwexpander_32.c
    ${OPUS_ROOT_PATH}/silk/bwexpander.c
    ${OPUS_ROOT_PATH}/silk/debug.c
    ${OPUS_ROOT_PATH}/silk/decode_pitch.c
    ${OPUS_ROOT_PATH}/silk/inner_prod_aligned.c
    ${OPUS_ROOT_PATH}/silk/lin2log.c
    ${OPUS_ROOT_PATH}/silk/log2lin.c
    ${OPUS_ROOT_PATH}/silk/LPC_analysis_filter.c
    ${OPUS_ROOT_PATH}/silk/LPC_inv_pred_gain.c
    ${OPUS_ROOT_PATH}/silk/table_LSF_cos.c
    ${OPUS_ROOT_PATH}/silk/NLSF2A.c
    ${OPUS_ROOT_PATH}/silk/NLSF_stabilize.c
    ${OPUS_ROOT_PATH}/silk/NLSF_VQ_weights_laroia.c
    ${OPUS_ROOT_PATH}/silk/pitch_est_tables.c
    ${OPUS_ROOT_PATH}/silk/resampler.c
    ${OPUS_ROOT_PATH}/silk/resampler_down2_3.c
    ${OPUS_ROOT_PATH}/silk/resampler_down2.c
    ${OPUS_ROOT_PATH}/silk/resampler_private_AR2.c
    ${OPUS_ROOT_PATH}/silk/resampler_private_down_FIR.c
    ${OPUS_ROOT_PATH}/silk/resampler_private_IIR_FIR.c
    ${OPUS_ROOT_PATH}/silk/resampler_private_up2_HQ.c
    ${OPUS_ROOT_PATH}/silk/resampler_rom.c
    ${OPUS_ROOT_PATH}/silk/sigm_Q15.c
    ${OPUS_ROOT_PATH}/silk/sort.c
    ${OPUS_ROOT_PATH}/silk/sum_sqr_shift.c
    ${OPUS_ROOT_PATH}/silk/stereo_decode_pred.c
    ${OPUS_ROOT_PATH}/silk/stereo_encode_pred.c
    ${OPUS_ROOT_PATH}/silk/stereo_find_predictor.c
    ${OPUS_ROOT_PATH}/silk/stereo_quant_pred.c
    ${OPUS_ROOT_PATH}/silk/LPC_fit.c
    ${OPUS_ROOT_PATH}/silk/float/apply_sine_window_FLP.c
    ${OPUS_ROOT_PATH}/silk/float/corrMatrix_FLP.c
    ${OPUS_ROOT_PATH}/silk/float/encode_frame_FLP.c
    ${OPUS_ROOT_PATH}/silk/float/find_LPC_FLP.c
    ${OPUS_ROOT_PATH}/silk/float/find_LTP_FLP.c
    ${OPUS_ROOT_PATH}/silk/float/find_pitch_lags_FLP.c
    ${OPUS_ROOT_PATH}/silk/float/find_pred_coefs_FLP.c
    ${OPUS_ROOT_PATH}/silk/float/LPC_analysis_filter_FLP.c
    ${OPUS_ROOT_PATH}/silk/float/LTP_analysis_filter_FLP.c
    ${OPUS_ROOT_PATH}/silk/float/LTP_scale_ctrl_FLP.c
    ${OPUS_ROOT_PATH}/silk/float/noise_shape_analysis_FLP.c
    ${OPUS_ROOT_PATH}/silk/float/process_gains_FLP.c
    ${OPUS_ROOT_PATH}/silk/float/regularize_correlations_FLP.c
    ${OPUS_ROOT_PATH}/silk/float/residual_energy_FLP.c
    ${OPUS_ROOT_PATH}/silk/float/warped_autocorrelation_FLP.c
    ${OPUS_ROOT_PATH}/silk/float/wrappers_FLP.c
    ${OPUS_ROOT_PATH}/silk/float/autocorrelation_FLP.c
    ${OPUS_ROOT_PATH}/silk/float/burg_modified_FLP.c
    ${OPUS_ROOT_PATH}/silk/float/bwexpander_FLP.c
    ${OPUS_ROOT_PATH}/silk/float/energy_FLP.c
    ${OPUS_ROOT_PATH}/silk/float/inner_product_FLP.c
    ${OPUS_ROOT_PATH}/silk/float/k2a_FLP.c
    ${OPUS_ROOT_PATH}/silk/float/LPC_inv_pred_gain_FLP.c
    ${OPUS_ROOT_PATH}/silk/float/pitch_analysis_core_FLP.c
    ${OPUS_ROOT_PATH}/silk/float/scale_copy_vector_FLP.c
    ${OPUS_ROOT_PATH}/silk/float/scale_vector_FLP.c
    ${OPUS_ROOT_PATH}/silk/float/schur_FLP.c
    ${OPUS_ROOT_PATH}/silk/float/sort_FLP.c
    # Fixed-point specific files for HarmonyOS
    ${OPUS_ROOT_PATH}/silk/fixed/LTP_analysis_filter_FIX.c
    ${OPUS_ROOT_PATH}/silk/fixed/LTP_scale_ctrl_FIX.c
    ${OPUS_ROOT_PATH}/silk/fixed/apply_sine_window_FIX.c
    ${OPUS_ROOT_PATH}/silk/fixed/autocorr_FIX.c
    ${OPUS_ROOT_PATH}/silk/fixed/burg_modified_FIX.c
    ${OPUS_ROOT_PATH}/silk/fixed/corrMatrix_FIX.c
    ${OPUS_ROOT_PATH}/silk/fixed/encode_frame_FIX.c
    ${OPUS_ROOT_PATH}/silk/fixed/find_LPC_FIX.c
    ${OPUS_ROOT_PATH}/silk/fixed/find_LTP_FIX.c
    ${OPUS_ROOT_PATH}/silk/fixed/find_pitch_lags_FIX.c
    ${OPUS_ROOT_PATH}/silk/fixed/find_pred_coefs_FIX.c
    ${OPUS_ROOT_PATH}/silk/fixed/k2a_FIX.c
    ${OPUS_ROOT_PATH}/silk/fixed/k2a_Q16_FIX.c
    ${OPUS_ROOT_PATH}/silk/fixed/noise_shape_analysis_FIX.c
    ${OPUS_ROOT_PATH}/silk/fixed/pitch_analysis_core_FIX.c
    ${OPUS_ROOT_PATH}/silk/fixed/process_gains_FIX.c
    ${OPUS_ROOT_PATH}/silk/fixed/regularize_correlations_FIX.c
    ${OPUS_ROOT_PATH}/silk/fixed/residual_energy16_FIX.c
    ${OPUS_ROOT_PATH}/silk/fixed/residual_energy_FIX.c
    ${OPUS_ROOT_PATH}/silk/fixed/schur64_FIX.c
    ${OPUS_ROOT_PATH}/silk/fixed/schur_FIX.c
    ${OPUS_ROOT_PATH}/silk/fixed/vector_ops_FIX.c
    ${OPUS_ROOT_PATH}/silk/fixed/warped_autocorrelation_FIX.c
)

# 创建Opus静态库
add_library(opus STATIC ${OPUS_SOURCES})

# 设置Opus编译选项（移除ARM特定选项）
target_compile_definitions(opus PRIVATE
    OPUS_BUILD
    HAVE_LRINT
    HAVE_LRINTF
    VAR_ARRAYS
    USE_ALLOCA
)

# 为HarmonyOS环境设置额外的编译选项
if(NOT APPLE)
    target_compile_definitions(opus PRIVATE
        __MUSL__
        FIXED_POINT
    )
endif()

# Main NAPI library
add_library(lib_napi SHARED
    napi_init.cpp
    audio_decoder.cpp
    opus_player.cpp
)

# 设置编译选项
target_compile_definitions(lib_napi PRIVATE
    OPUS_BUILD
    HAVE_LRINT
    HAVE_LRINTF
    VAR_ARRAYS
)

# 为HarmonyOS环境设置额外的编译选项
if(NOT APPLE)
    target_compile_definitions(lib_napi PRIVATE
        __MUSL__
    )
endif()

# 设置C++标准
set_property(TARGET lib_napi PROPERTY CXX_STANDARD 17)

# 链接库
if(APPLE)
    # macOS开发环境
    target_link_libraries(lib_napi PUBLIC opus)
else()
    # HarmonyOS环境
    target_link_libraries(lib_napi PUBLIC
        libace_napi.z.so
        opus
        log
    )
endif()