import {
  createOpusPlayer,
  loadOpusData,
  playOpus,
  pauseOpus,
  stopOpus,
  setVolume,
  getPlayState,
  getCurrentPosition,
  getDuration,
  releaseOpusPlayer,
  testNapi,
  checkNapiModule
} from '../../../../Index';
import fs from '@ohos.file.fs';

/**
 * Opus播放器演示页面
 * 展示如何使用NAPI调用C++的Opus播放功能
 */
@Entry
@Component
export struct OpusPlayerDemo {
  @State private playerId: number = -1;
  @State private currentState: number = 0; // 0=停止, 1=播放, 2=暂停
  @State private currentPosition: number = 0;
  @State private totalDuration: number = 0;
  @State private volume: number = 1.0;
  @State private statusText: string = '未初始化';
  @State private isLoading: boolean = false;
  @State private filePath: string = '';

  aboutToAppear() {
    this.initializePlayer();
  }

  aboutToDisappear() {
    this.releasePlayer();
  }

  /**
   * 初始化播放器
   */
  private async initializePlayer() {
    try {
      this.statusText = '正在检查NAPI模块...';

      // 检查NAPI模块状态
      const moduleStatus = checkNapiModule();
      console.log('NAPI模块状态:', moduleStatus);

      if (!moduleStatus.loaded) {
        this.statusText = `NAPI模块未加载: ${moduleStatus.error}\n\n请按照BUILD_GUIDE.md中的说明构建liblib_napi.so文件`;
        return;
      }

      // 测试NAPI连接
      const testResult = testNapi();
      console.log('NAPI测试结果:', testResult);

      // 创建Opus播放器
      this.playerId = createOpusPlayer(48000, 2);

      if (this.playerId === -1) {
        this.statusText = '播放器初始化失败';
        return;
      }

      this.statusText = `播放器初始化成功 (ID: ${this.playerId})`;

      // 启动状态更新定时器
      this.startStatusUpdater();

    } catch (error) {
      this.statusText = `初始化错误: ${error}`;
    }
  }

  /**
   * 释放播放器
   */
  private releasePlayer() {
    if (this.playerId !== -1) {
      releaseOpusPlayer(this.playerId);
      this.playerId = -1;
    }
  }

  /**
   * 启动状态更新定时器
   */
  private startStatusUpdater() {
    setInterval(() => {
      if (this.playerId !== -1) {
        this.currentState = getPlayState(this.playerId);
        this.currentPosition = getCurrentPosition(this.playerId);
        this.totalDuration = getDuration(this.playerId);
        this.updateStatusText();
      }
    }, 500);
  }

  /**
   * 更新状态文本
   */
  private updateStatusText() {
    switch (this.currentState) {
      case 0:
        this.statusText = '已停止';
        break;
      case 1:
        this.statusText = `播放中 ${this.formatTime(this.currentPosition)}/${this.formatTime(this.totalDuration)}`;
        break;
      case 2:
        this.statusText = `已暂停 ${this.formatTime(this.currentPosition)}/${this.formatTime(this.totalDuration)}`;
        break;
      default:
        this.statusText = '未知状态';
        break;
    }
  }

  /**
   * 格式化时间显示
   */
  private formatTime(milliseconds: number): string {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  /**
   * 加载Opus音频文件
   */
  private async loadOpusFile() {
    if (this.playerId === -1 || this.isLoading) {
      return;
    }

    const testFilePath = this.filePath || '/data/storage/el2/base/haps/entry/files/test.opus';

    try {
      this.isLoading = true;
      this.statusText = '正在加载Opus文件...';
      
      // 读取文件
      const file = fs.openSync(testFilePath, fs.OpenMode.READ_ONLY);
      const stat = fs.statSync(testFilePath);
      const buffer = new ArrayBuffer(stat.size);
      
      fs.readSync(file.fd, buffer);
      fs.closeSync(file);

      // 加载到播放器
      const success = loadOpusData(this.playerId, buffer);
      
      if (success) {
        this.totalDuration = getDuration(this.playerId);
        this.statusText = 'Opus文件加载成功';
      } else {
        this.statusText = 'Opus文件加载失败';
      }

    } catch (error) {
      this.statusText = `加载错误: ${error}`;
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 播放音频
   */
  private playAudio() {
    if (this.playerId !== -1 && !this.isLoading) {
      const success = playOpus(this.playerId);
      if (!success) {
        this.statusText = '播放失败';
      }
    }
  }

  /**
   * 暂停音频
   */
  private pauseAudio() {
    if (this.playerId !== -1 && !this.isLoading) {
      const success = pauseOpus(this.playerId);
      if (!success) {
        this.statusText = '暂停失败';
      }
    }
  }

  /**
   * 停止音频
   */
  private stopAudio() {
    if (this.playerId !== -1 && !this.isLoading) {
      const success = stopOpus(this.playerId);
      if (!success) {
        this.statusText = '停止失败';
      }
    }
  }

  /**
   * 设置音量
   */
  private setAudioVolume(volume: number) {
    if (this.playerId !== -1) {
      this.volume = volume;
      setVolume(this.playerId, volume);
    }
  }

  build() {
    Column({ space: 20 }) {
      // 标题
      Text('Opus音频播放器 (NAPI)')
        .fontSize(24)
        .fontWeight(FontWeight.Bold)
        .margin({ top: 40 })

      // 状态显示
      Text(this.statusText)
        .fontSize(16)
        .fontColor('#666666')
        .textAlign(TextAlign.Center)
        .width('100%')
        .padding(10)
        .backgroundColor('#F0F0F0')
        .borderRadius(8)

      // 文件路径输入
      Column() {
        Text('Opus文件路径:')
          .fontSize(14)
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 8 })

        TextInput({ placeholder: '请输入.opus文件路径', text: this.filePath })
          .width('100%')
          .height(40)
          .fontSize(14)
          .onChange((value: string) => {
            this.filePath = value;
          })
      }
      .width('90%')

      // 进度条
      if (this.totalDuration > 0) {
        Column() {
          Slider({
            value: this.totalDuration > 0 ? (this.currentPosition / this.totalDuration) * 100 : 0,
            min: 0,
            max: 100,
            step: 1
          })
            .width('100%')
            .trackColor('#E0E0E0')
            .selectedColor('#007AFF')
            .blockColor('#007AFF')

          Row() {
            Text(this.formatTime(this.currentPosition))
              .fontSize(12)
              .fontColor('#999999')
            
            Blank()
            
            Text(this.formatTime(this.totalDuration))
              .fontSize(12)
              .fontColor('#999999')
          }
          .width('100%')
          .margin({ top: 8 })
        }
        .width('90%')
      }

      // 控制按钮
      Row({ space: 15 }) {
        Button('加载文件')
          .fontSize(14)
          .enabled(!this.isLoading && this.playerId !== -1)
          .onClick(() => this.loadOpusFile())

        Button('播放')
          .fontSize(14)
          .enabled(!this.isLoading && this.currentState !== 1)
          .onClick(() => this.playAudio())

        Button('暂停')
          .fontSize(14)
          .enabled(!this.isLoading && this.currentState === 1)
          .onClick(() => this.pauseAudio())

        Button('停止')
          .fontSize(14)
          .enabled(!this.isLoading && this.currentState !== 0)
          .onClick(() => this.stopAudio())
      }

      // 音量控制
      Column() {
        Text(`音量: ${Math.round(this.volume * 100)}%`)
          .fontSize(14)
          .margin({ bottom: 10 })

        Slider({
          value: this.volume * 100,
          min: 0,
          max: 100,
          step: 1
        })
          .width('80%')
          .trackColor('#E0E0E0')
          .selectedColor('#007AFF')
          .blockColor('#007AFF')
          .onChange((value: number) => {
            this.setAudioVolume(value / 100);
          })
      }

      Blank()

      // 说明文本
      Text('使用说明：\n1. 输入.opus文件的完整路径\n2. 点击"加载文件"加载音频\n3. 使用播放控制按钮控制播放\n4. 拖动滑块调节音量\n\n基于opus-1.4库的NAPI实现')
        .fontSize(12)
        .fontColor('#999999')
        .textAlign(TextAlign.Center)
        .margin({ bottom: 40 })
    }
    .width('100%')
    .height('100%')
    .padding(20)
    .backgroundColor('#F5F5F5')
  }
}
