#!/bin/bash

# Opus功能测试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TEST_BUILD_DIR="$SCRIPT_DIR/test_build"

# 清理测试构建目录
clean_test() {
    log_info "清理测试构建目录..."
    if [ -d "$TEST_BUILD_DIR" ]; then
        rm -rf "$TEST_BUILD_DIR"
        log_success "测试构建目录已清理"
    fi
}

# 构建测试程序
build_test() {
    log_info "构建Opus功能测试程序..."
    
    # 创建测试构建目录
    mkdir -p "$TEST_BUILD_DIR"
    cd "$TEST_BUILD_DIR"
    
    # 生成config.h文件
    log_info "生成Opus配置文件..."
    cat > "$SCRIPT_DIR/opus-1.5.2/config.h" << 'EOF'
#ifndef CONFIG_H
#define CONFIG_H
#define HAVE_LRINT 1
#define HAVE_LRINTF 1
#define VAR_ARRAYS 1
#define USE_ALLOCA 1
#define OPUS_BUILD 1
#define VERSION "1.5.2"
#define PACKAGE_VERSION "1.5.2"
#endif
EOF
    
    # 复制CMakeLists.txt文件
    cp "$SCRIPT_DIR/CMakeLists_test.txt" "$TEST_BUILD_DIR/CMakeLists.txt"

    # 运行CMake配置
    log_info "运行CMake配置..."
    cmake . -DCMAKE_BUILD_TYPE=Release -DCMAKE_VERBOSE_MAKEFILE=ON
    
    # 编译测试程序
    log_info "编译测试程序..."
    make -j$(nproc)
    
    log_success "测试程序构建完成"
}

# 运行测试
run_test() {
    log_info "运行Opus功能测试..."
    
    cd "$TEST_BUILD_DIR"
    
    if [ -f "./test_opus" ]; then
        log_info "执行测试程序..."
        ./test_opus
        log_success "测试程序执行完成"
    else
        log_error "测试程序不存在"
        return 1
    fi
}

# 显示帮助信息
show_help() {
    echo "Opus功能测试脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  build     构建测试程序"
    echo "  test      运行测试程序"
    echo "  clean     清理测试文件"
    echo "  all       清理、构建并运行测试 (默认)"
    echo "  help      显示此帮助信息"
    echo ""
}

# 主函数
main() {
    local action="${1:-all}"
    
    case "$action" in
        "build")
            build_test
            ;;
        "test")
            run_test
            ;;
        "clean")
            clean_test
            ;;
        "all")
            clean_test
            build_test
            run_test
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知选项: $action"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
