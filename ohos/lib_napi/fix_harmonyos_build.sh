#!/bin/bash

# HarmonyOS构建问题修复脚本
# 此脚本用于修复在HarmonyOS环境中构建时遇到的常见问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CPP_DIR="$SCRIPT_DIR/src/main/cpp"

log_info "开始修复HarmonyOS构建问题..."

# 1. 检查并修复opus_player.cpp中的问题
fix_opus_player() {
    log_info "修复opus_player.cpp..."
    
    local file="$CPP_DIR/opus_player.cpp"
    
    if [ ! -f "$file" ]; then
        log_error "文件不存在: $file"
        return 1
    fi
    
    # 检查是否已经修复了宏重定义问题
    if grep -q "#undef LOG_DOMAIN" "$file"; then
        log_success "opus_player.cpp 宏重定义问题已修复"
    else
        log_warning "opus_player.cpp 宏重定义问题需要手动修复"
    fi
    
    # 检查是否已经修复了类型转换问题
    if grep -q "static_cast<int64_t>(0)" "$file"; then
        log_success "opus_player.cpp 类型转换问题已修复"
    else
        log_warning "opus_player.cpp 类型转换问题需要手动修复"
    fi
}

# 2. 检查并修复audio_decoder.cpp中的问题
fix_audio_decoder() {
    log_info "修复audio_decoder.cpp..."
    
    local file="$CPP_DIR/audio_decoder.cpp"
    
    if [ ! -f "$file" ]; then
        log_error "文件不存在: $file"
        return 1
    fi
    
    # 检查是否已经修复了宏重定义问题
    if grep -q "#undef LOG_DOMAIN" "$file"; then
        log_success "audio_decoder.cpp 宏重定义问题已修复"
    else
        log_warning "audio_decoder.cpp 宏重定义问题需要手动修复"
    fi
}

# 3. 检查CMakeLists.txt配置
check_cmake_config() {
    log_info "检查CMakeLists.txt配置..."
    
    local file="$CPP_DIR/CMakeLists.txt"
    
    if [ ! -f "$file" ]; then
        log_error "文件不存在: $file"
        return 1
    fi
    
    # 检查HarmonyOS特定配置
    if grep -q "__MUSL__" "$file"; then
        log_success "CMakeLists.txt HarmonyOS配置已添加"
    else
        log_warning "CMakeLists.txt 缺少HarmonyOS特定配置"
    fi
    
    # 检查链接库配置
    if grep -q "libace_napi.z.so" "$file"; then
        log_success "CMakeLists.txt 链接库配置正确"
    else
        log_warning "CMakeLists.txt 链接库配置可能有问题"
    fi
}

# 4. 验证Opus源码完整性
check_opus_sources() {
    log_info "检查Opus源码完整性..."
    
    local opus_dir="$SCRIPT_DIR/opus-1.5.2"
    
    if [ ! -d "$opus_dir" ]; then
        log_error "Opus源码目录不存在: $opus_dir"
        return 1
    fi
    
    # 检查关键文件
    local key_files=(
        "include/opus.h"
        "src/opus.c"
        "src/opus_decoder.c"
        "celt/celt.c"
        "silk/dec_API.c"
    )
    
    local missing_files=0
    for file in "${key_files[@]}"; do
        if [ ! -f "$opus_dir/$file" ]; then
            log_error "缺少关键文件: $file"
            missing_files=$((missing_files + 1))
        fi
    done
    
    if [ $missing_files -eq 0 ]; then
        log_success "Opus源码完整性检查通过"
    else
        log_error "发现 $missing_files 个缺失文件"
        return 1
    fi
}

# 5. 生成HarmonyOS兼容的config.h
generate_harmonyos_config() {
    log_info "生成HarmonyOS兼容的config.h..."
    
    local config_file="$SCRIPT_DIR/opus-1.5.2/config.h"
    
    cat > "$config_file" << 'EOF'
#ifndef CONFIG_H
#define CONFIG_H

/* HarmonyOS specific configuration for Opus */

/* Define to 1 if you have the <inttypes.h> header file. */
#define HAVE_INTTYPES_H 1

/* Define to 1 if you have the `lrint' function. */
#define HAVE_LRINT 1

/* Define to 1 if you have the `lrintf' function. */
#define HAVE_LRINTF 1

/* Define to 1 if you have the <stdint.h> header file. */
#define HAVE_STDINT_H 1

/* Define to 1 if you have the <stdlib.h> header file. */
#define HAVE_STDLIB_H 1

/* Define to 1 if you have the <string.h> header file. */
#define HAVE_STRING_H 1

/* Define to 1 if you have the <unistd.h> header file. */
#define HAVE_UNISTD_H 1

/* Name of package */
#define PACKAGE "opus"

/* Define to the version of this package. */
#define PACKAGE_VERSION "1.5.2"

/* Version number of package */
#define VERSION "1.5.2"

/* Use C99 variable-length arrays */
#define VAR_ARRAYS 1

/* Use alloca() */
#define USE_ALLOCA 1

/* Define OPUS_BUILD if building Opus */
#define OPUS_BUILD 1

/* Use fixed-point arithmetic for HarmonyOS */
#define FIXED_POINT 1

/* HarmonyOS MUSL compatibility */
#define __MUSL__ 1

#endif /* CONFIG_H */
EOF
    
    log_success "config.h 已生成"
}

# 6. 创建构建验证脚本
create_build_verification() {
    log_info "创建构建验证脚本..."
    
    cat > "$SCRIPT_DIR/verify_build.sh" << 'EOF'
#!/bin/bash

# HarmonyOS构建验证脚本

echo "=== HarmonyOS构建验证 ==="

# 检查.so文件是否生成
SO_FILES=$(find . -name "liblib_napi.so" -type f 2>/dev/null)
if [ -n "$SO_FILES" ]; then
    echo "✅ 找到NAPI库文件:"
    echo "$SO_FILES" | while read file; do
        echo "  - $file ($(ls -lh "$file" | awk '{print $5}'))"
    done
else
    echo "❌ 未找到liblib_napi.so文件"
fi

# 检查构建目录
BUILD_DIRS=$(find . -name ".cxx" -type d 2>/dev/null)
if [ -n "$BUILD_DIRS" ]; then
    echo "✅ 找到构建目录:"
    echo "$BUILD_DIRS"
else
    echo "⚠️  未找到.cxx构建目录"
fi

# 检查关键源文件
echo "✅ 源文件检查:"
[ -f "src/main/cpp/opus_player.cpp" ] && echo "  - opus_player.cpp ✅" || echo "  - opus_player.cpp ❌"
[ -f "src/main/cpp/audio_decoder.cpp" ] && echo "  - audio_decoder.cpp ✅" || echo "  - audio_decoder.cpp ❌"
[ -f "src/main/cpp/napi_init.cpp" ] && echo "  - napi_init.cpp ✅" || echo "  - napi_init.cpp ❌"
[ -f "src/main/cpp/CMakeLists.txt" ] && echo "  - CMakeLists.txt ✅" || echo "  - CMakeLists.txt ❌"

echo "=== 验证完成 ==="
EOF
    
    chmod +x "$SCRIPT_DIR/verify_build.sh"
    log_success "构建验证脚本已创建"
}

# 主函数
main() {
    log_info "HarmonyOS构建问题修复脚本 v1.0"
    log_info "================================"
    
    fix_opus_player
    fix_audio_decoder
    check_cmake_config
    check_opus_sources
    generate_harmonyos_config
    create_build_verification
    
    log_info "================================"
    log_success "修复脚本执行完成！"
    
    echo ""
    log_info "接下来的步骤："
    echo "1. 在DevEco Studio中重新构建项目"
    echo "2. 运行 ./verify_build.sh 验证构建结果"
    echo "3. 如果仍有问题，请查看 HARMONYOS_BUILD.md"
    echo ""
    log_info "构建命令示例："
    echo "  hvigor assembleHap --mode module -p module=lib_napi"
}

# 运行主函数
main "$@"
