#include <iostream>
#include <vector>
#include <memory>
#include "src/main/cpp/opus_player.h"
#include "src/main/cpp/audio_decoder.h"

/**
 * 简单的Opus功能测试程序
 * 用于验证Opus库和播放器功能是否正常
 */

void testOpusDecoder() {
    std::cout << "=== 测试Opus解码器 ===" << std::endl;
    
    // 创建Opus解码器
    auto decoder = AudioDecoderFactory::createDecoder(AudioDecoderFactory::CodecType::OPUS);
    if (!decoder) {
        std::cout << "❌ 创建Opus解码器失败" << std::endl;
        return;
    }
    
    // 初始化解码器
    if (!decoder->initialize(48000, 2)) {
        std::cout << "❌ 初始化Opus解码器失败" << std::endl;
        return;
    }
    
    std::cout << "✅ Opus解码器创建和初始化成功" << std::endl;
    std::cout << "   采样率: " << decoder->getSampleRate() << "Hz" << std::endl;
    std::cout << "   声道数: " << decoder->getChannels() << std::endl;
    
    decoder->release();
    std::cout << "✅ Opus解码器释放成功" << std::endl;
}

void testOpusPlayer() {
    std::cout << "\n=== 测试Opus播放器 ===" << std::endl;
    
    // 创建播放器
    OpusPlayer player;
    
    // 初始化播放器
    if (!player.initialize(48000, 2)) {
        std::cout << "❌ 初始化Opus播放器失败" << std::endl;
        return;
    }
    
    std::cout << "✅ Opus播放器初始化成功" << std::endl;
    std::cout << "   状态: " << static_cast<int>(player.getState()) << std::endl;
    std::cout << "   音量: " << player.getVolume() << std::endl;
    std::cout << "   位置: " << player.getCurrentPosition() << "ms" << std::endl;
    std::cout << "   时长: " << player.getDuration() << "ms" << std::endl;
    
    // 测试音量设置
    player.setVolume(0.5f);
    std::cout << "✅ 音量设置成功: " << player.getVolume() << std::endl;
    
    // 创建一些模拟的Opus数据（实际上这不是有效的Opus数据）
    std::vector<unsigned char> mockOpusData = {
        0x4F, 0x70, 0x75, 0x73, 0x48, 0x65, 0x61, 0x64, // "OpusHead"
        0x01, 0x02, 0x00, 0x0F, 0x80, 0x3E, 0x00, 0x00, // 版本和配置
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00  // 填充数据
    };
    
    // 尝试加载数据（预期会失败，因为这不是有效的Opus文件）
    bool loadResult = player.loadOpusData(mockOpusData.data(), mockOpusData.size());
    if (loadResult) {
        std::cout << "✅ 模拟数据加载成功（意外）" << std::endl;
    } else {
        std::cout << "⚠️  模拟数据加载失败（预期结果）" << std::endl;
    }
    
    player.release();
    std::cout << "✅ Opus播放器释放成功" << std::endl;
}

void testAudioDecoderFactory() {
    std::cout << "\n=== 测试音频解码器工厂 ===" << std::endl;
    
    // 检查支持的编解码器
    auto supportedCodecs = AudioDecoderFactory::getSupportedCodecs();
    std::cout << "支持的编解码器数量: " << supportedCodecs.size() << std::endl;
    
    for (auto codec : supportedCodecs) {
        std::cout << "  - 编解码器类型: " << static_cast<int>(codec) << std::endl;
    }
    
    // 检查Opus支持
    bool opusSupported = AudioDecoderFactory::isSupported(AudioDecoderFactory::CodecType::OPUS);
    std::cout << "Opus支持: " << (opusSupported ? "✅ 是" : "❌ 否") << std::endl;
}

void printSystemInfo() {
    std::cout << "=== 系统信息 ===" << std::endl;
    
#ifdef __APPLE__
    std::cout << "平台: macOS (开发环境)" << std::endl;
#else
    std::cout << "平台: HarmonyOS" << std::endl;
#endif
    
    std::cout << "编译器: " << __VERSION__ << std::endl;
    std::cout << "C++标准: " << __cplusplus << std::endl;
    
#ifdef OPUS_BUILD
    std::cout << "Opus构建标志: 已定义" << std::endl;
#else
    std::cout << "Opus构建标志: 未定义" << std::endl;
#endif
    
#ifdef HAVE_LRINT
    std::cout << "LRINT支持: 已启用" << std::endl;
#endif
    
#ifdef VAR_ARRAYS
    std::cout << "可变长数组: 已启用" << std::endl;
#endif
}

int main() {
    std::cout << "🎵 Opus音频播放器功能测试 🎵" << std::endl;
    std::cout << "==============================" << std::endl;
    
    try {
        printSystemInfo();
        testAudioDecoderFactory();
        testOpusDecoder();
        testOpusPlayer();
        
        std::cout << "\n==============================" << std::endl;
        std::cout << "✅ 所有测试完成！" << std::endl;
        std::cout << "📝 注意: 这只是基本功能测试，实际音频播放需要有效的Opus文件。" << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "❌ 测试过程中发生异常: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
