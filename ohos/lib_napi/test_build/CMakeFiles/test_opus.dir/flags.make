# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# compile CXX with /usr/bin/c++
CXX_DEFINES = -DHAVE_LRINT -DHAVE_LRINTF -DOPUS_BUILD -DVAR_ARRAYS

CXX_INCLUDES = -I/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/.. -I/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/../opus-1.5.2/include -I/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/../opus-1.5.2/src -I/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/../opus-1.5.2/celt -I/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/../opus-1.5.2/silk -I/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/../opus-1.5.2/silk/float

CXX_FLAGSarm64 = -O3 -DNDEBUG -std=gnu++17 -arch arm64

CXX_FLAGS = -O3 -DNDEBUG -std=gnu++17 -arch arm64

