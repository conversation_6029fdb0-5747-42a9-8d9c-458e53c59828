# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build

# Include any dependencies generated for this target.
include CMakeFiles/test_opus.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/test_opus.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/test_opus.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/test_opus.dir/flags.make

CMakeFiles/test_opus.dir/codegen:
.PHONY : CMakeFiles/test_opus.dir/codegen

CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.cpp.o: CMakeFiles/test_opus.dir/flags.make
CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.cpp.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.cpp
CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.cpp.o: CMakeFiles/test_opus.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.cpp.o -MF CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.cpp.o.d -o CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.cpp.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.cpp

CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.cpp > CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.cpp.i

CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.cpp -o CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.cpp.s

CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.cpp.o: CMakeFiles/test_opus.dir/flags.make
CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.cpp.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.cpp
CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.cpp.o: CMakeFiles/test_opus.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.cpp.o -MF CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.cpp.o.d -o CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.cpp.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.cpp

CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.cpp > CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.cpp.i

CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.cpp -o CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.cpp.s

CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.cpp.o: CMakeFiles/test_opus.dir/flags.make
CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.cpp.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.cpp
CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.cpp.o: CMakeFiles/test_opus.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.cpp.o -MF CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.cpp.o.d -o CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.cpp.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.cpp

CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.cpp > CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.cpp.i

CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.cpp -o CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.cpp.s

# Object files for target test_opus
test_opus_OBJECTS = \
"CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.cpp.o" \
"CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.cpp.o" \
"CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.cpp.o"

# External object files for target test_opus
test_opus_EXTERNAL_OBJECTS =

test_opus: CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.cpp.o
test_opus: CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.cpp.o
test_opus: CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.cpp.o
test_opus: CMakeFiles/test_opus.dir/build.make
test_opus: libopus_test.a
test_opus: CMakeFiles/test_opus.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Linking CXX executable test_opus"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/test_opus.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/test_opus.dir/build: test_opus
.PHONY : CMakeFiles/test_opus.dir/build

CMakeFiles/test_opus.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/test_opus.dir/cmake_clean.cmake
.PHONY : CMakeFiles/test_opus.dir/clean

CMakeFiles/test_opus.dir/depend:
	cd /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles/test_opus.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/test_opus.dir/depend

