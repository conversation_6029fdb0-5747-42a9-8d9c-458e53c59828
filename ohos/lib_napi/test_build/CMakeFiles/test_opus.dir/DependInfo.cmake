
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.cpp" "CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.cpp.o" "gcc" "CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.cpp.o.d"
  "/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.cpp" "CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.cpp.o" "gcc" "CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.cpp.o.d"
  "/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.cpp" "CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.cpp.o" "gcc" "CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.cpp.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
