/usr/bin/c++ -O3 -DNDEBUG -arch arm64 -Wl,-search_paths_first -Wl,-headerpad_max_install_names "CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.cpp.o" "CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.cpp.o" "CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.cpp.o" -o test_opus  libopus_test.a
