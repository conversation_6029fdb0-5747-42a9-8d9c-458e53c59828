# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/opus_test.dir/all
all: CMakeFiles/test_opus.dir/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/opus_test.dir/codegen
codegen: CMakeFiles/test_opus.dir/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/opus_test.dir/clean
clean: CMakeFiles/test_opus.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/opus_test.dir

# All Build rule for target.
CMakeFiles/opus_test.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97 "Built target opus_test"
.PHONY : CMakeFiles/opus_test.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/opus_test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles 97
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/opus_test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles 0
.PHONY : CMakeFiles/opus_test.dir/rule

# Convenience name for target.
opus_test: CMakeFiles/opus_test.dir/rule
.PHONY : opus_test

# codegen rule for target.
CMakeFiles/opus_test.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97 "Finished codegen for target opus_test"
.PHONY : CMakeFiles/opus_test.dir/codegen

# clean rule for target.
CMakeFiles/opus_test.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/clean
.PHONY : CMakeFiles/opus_test.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/test_opus.dir

# All Build rule for target.
CMakeFiles/test_opus.dir/all: CMakeFiles/opus_test.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_opus.dir/build.make CMakeFiles/test_opus.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_opus.dir/build.make CMakeFiles/test_opus.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=98,99,100 "Built target test_opus"
.PHONY : CMakeFiles/test_opus.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/test_opus.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles 100
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/test_opus.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles 0
.PHONY : CMakeFiles/test_opus.dir/rule

# Convenience name for target.
test_opus: CMakeFiles/test_opus.dir/rule
.PHONY : test_opus

# codegen rule for target.
CMakeFiles/test_opus.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_opus.dir/build.make CMakeFiles/test_opus.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=98,99,100 "Finished codegen for target test_opus"
.PHONY : CMakeFiles/test_opus.dir/codegen

# clean rule for target.
CMakeFiles/test_opus.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_opus.dir/build.make CMakeFiles/test_opus.dir/clean
.PHONY : CMakeFiles/test_opus.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

