# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build

# Include any dependencies generated for this target.
include CMakeFiles/opus_test.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/opus_test.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/opus_test.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/opus_test.dir/flags.make

CMakeFiles/opus_test.dir/codegen:
.PHONY : CMakeFiles/opus_test.dir/codegen

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_25) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_26) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_27) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_28) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_29) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_30) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_31) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_32) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_33) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_34) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_35) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_36) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_37) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_38) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_39) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_40) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_41) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_42) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_43) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_44) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_45) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_46) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_47) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_48) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_49) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_50) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_51) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_52) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_53) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_54) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_55) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_56) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_57) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_58) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_59) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_60) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_61) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_62) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_63) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_64) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_65) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_66) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_67) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_68) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_69) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_70) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_71) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_72) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_73) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_74) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_75) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_76) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_77) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_78) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_79) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_80) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_81) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_82) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_83) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_84) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_85) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_86) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_87) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_88) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_89) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_90) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_91) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_92) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_93) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_94) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_95) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_96) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_97) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_98) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_99) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_100) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_101) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_102) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_103) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_104) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_105) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_106) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_107) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_108) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_109) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_110) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_111) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_112) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_113) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_114) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_115) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_116) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_117) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_118) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_119) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_120) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_121) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_122) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_123) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_124) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_125) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_126) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_127) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_128) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_129) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.c.s

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.c.o: CMakeFiles/opus_test.dir/flags.make
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.c.o: /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.c
CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.c.o: CMakeFiles/opus_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_130) "Building C object CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.c.o -MF CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.c.o.d -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.c.o -c /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.c

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.c > CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.c.i

CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.c -o CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.c.s

# Object files for target opus_test
opus_test_OBJECTS = \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.c.o" \
"CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.c.o"

# External object files for target opus_test
opus_test_EXTERNAL_OBJECTS =

libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.c.o
libopus_test.a: CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.c.o
libopus_test.a: CMakeFiles/opus_test.dir/build.make
libopus_test.a: CMakeFiles/opus_test.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_131) "Linking C static library libopus_test.a"
	$(CMAKE_COMMAND) -P CMakeFiles/opus_test.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/opus_test.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/opus_test.dir/build: libopus_test.a
.PHONY : CMakeFiles/opus_test.dir/build

CMakeFiles/opus_test.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/opus_test.dir/cmake_clean.cmake
.PHONY : CMakeFiles/opus_test.dir/clean

CMakeFiles/opus_test.dir/depend:
	cd /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles/opus_test.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/opus_test.dir/depend

