# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# compile C with /usr/bin/cc
C_DEFINES = -DHAVE_LRINT -DHAVE_LRINTF -DOPUS_BUILD -DUSE_ALLOCA -DVAR_ARRAYS

C_INCLUDES = -I/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/.. -I/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/../opus-1.5.2/include -I/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/../opus-1.5.2/src -I/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/../opus-1.5.2/celt -I/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/../opus-1.5.2/silk -I/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/../opus-1.5.2/silk/float

C_FLAGSarm64 = -O3 -DNDEBUG -arch arm64

C_FLAGS = -O3 -DNDEBUG -arch arm64

