# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/opt/homebrew/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/opt/homebrew/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named opus_test

# Build rule for target.
opus_test: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 opus_test
.PHONY : opus_test

# fast build rule for target.
opus_test/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/build
.PHONY : opus_test/fast

#=============================================================================
# Target rules for targets named test_opus

# Build rule for target.
test_opus: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test_opus
.PHONY : test_opus

# fast build rule for target.
test_opus/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_opus.dir/build.make CMakeFiles/test_opus.dir/build
.PHONY : test_opus/fast

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.c.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.c.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.c.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.c.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/opus_test.dir/build.make CMakeFiles/opus_test.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.c.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.c.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.cpp.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_opus.dir/build.make CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.cpp.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.cpp.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.cpp.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_opus.dir/build.make CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.cpp.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.cpp.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.cpp.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_opus.dir/build.make CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.cpp.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.cpp.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.cpp.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_opus.dir/build.make CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.cpp.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.cpp.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.cpp.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_opus.dir/build.make CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.cpp.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.cpp.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.cpp.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_opus.dir/build.make CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.cpp.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.cpp.s

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.o: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.cpp.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.o

# target to build an object file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_opus.dir/build.make CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.cpp.o
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.cpp.o

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.i: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.cpp.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.i

# target to preprocess a source file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_opus.dir/build.make CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.cpp.i
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.cpp.i

Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.s: Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.cpp.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.s

# target to generate assembly for a file
Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_opus.dir/build.make CMakeFiles/test_opus.dir/Users/<USER>/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.cpp.s
.PHONY : Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... opus_test"
	@echo "... test_opus"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/bands.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_decoder.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_encoder.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/celt_lpc.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/cwrs.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entcode.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entdec.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/entenc.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/kiss_fft.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/laplace.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mathops.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/mdct.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/modes.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/pitch.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/quant_bands.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/rate.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/celt/vq.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/A2NLSF.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/CNG.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/HP_variable_cutoff.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_analysis_filter.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_fit.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LPC_inv_pred_gain.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/LP_variable_cutoff.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF2A.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_VQ_weights_laroia.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_decode.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_del_dec_quant.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_encode.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_stabilize.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NLSF_unpack.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/NSQ_del_dec.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/PLC.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VAD.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/VQ_WMat_EC.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/ana_filt_bank_1.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/biquad_alt.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/bwexpander_32.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/check_control_input.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/code_signs.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_SNR.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_audio_bandwidth.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/control_codec.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/debug.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/dec_API.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_core.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_frame.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_indices.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_parameters.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pitch.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decode_pulses.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/decoder_set_fs.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/enc_API.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_indices.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/encode_pulses.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_analysis_filter_FLP.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LPC_inv_pred_gain_FLP.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_analysis_filter_FLP.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/LTP_scale_ctrl_FLP.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/apply_sine_window_FLP.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/autocorrelation_FLP.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/burg_modified_FLP.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/bwexpander_FLP.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/corrMatrix_FLP.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/encode_frame_FLP.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/energy_FLP.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LPC_FLP.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_LTP_FLP.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pitch_lags_FLP.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/find_pred_coefs_FLP.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/inner_product_FLP.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/k2a_FLP.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/noise_shape_analysis_FLP.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/pitch_analysis_core_FLP.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/process_gains_FLP.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/regularize_correlations_FLP.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/residual_energy_FLP.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_copy_vector_FLP.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/scale_vector_FLP.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/schur_FLP.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/sort_FLP.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/warped_autocorrelation_FLP.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/float/wrappers_FLP.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/gain_quant.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_decoder.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/init_encoder.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/inner_prod_aligned.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/interpolate.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/lin2log.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/log2lin.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/pitch_est_tables.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/process_NLSFs.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/quant_LTP_gains.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_down2_3.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_AR2.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_IIR_FIR.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_down_FIR.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_private_up2_HQ.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/resampler_rom.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/shell_coder.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sigm_Q15.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sort.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_LR_to_MS.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_MS_to_LR.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_decode_pred.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_encode_pred.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_find_predictor.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/stereo_quant_pred.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/sum_sqr_shift.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/table_LSF_cos.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_LTP.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_NB_MB.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_NLSF_CB_WB.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_gain.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_other.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pitch_lag.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/silk/tables_pulses_per_block.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_decoder.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_encoder.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_decoder.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/opus_multistream_encoder.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/opus-1.5.2/src/repacketizer.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/audio_decoder.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/src/main/cpp/opus_player.s"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.o"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.i"
	@echo "... Users/zick/root/project/vscodeProjects/gs-fit-flutter/ohos/lib_napi/test_opus.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

