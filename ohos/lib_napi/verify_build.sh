#!/bin/bash

# HarmonyOS构建验证脚本

echo "=== HarmonyOS构建验证 ==="

# 检查.so文件是否生成
SO_FILES=$(find . -name "liblib_napi.so" -type f 2>/dev/null)
if [ -n "$SO_FILES" ]; then
    echo "✅ 找到NAPI库文件:"
    echo "$SO_FILES" | while read file; do
        echo "  - $file ($(ls -lh "$file" | awk '{print $5}'))"
    done
else
    echo "❌ 未找到liblib_napi.so文件"
fi

# 检查构建目录
BUILD_DIRS=$(find . -name ".cxx" -type d 2>/dev/null)
if [ -n "$BUILD_DIRS" ]; then
    echo "✅ 找到构建目录:"
    echo "$BUILD_DIRS"
else
    echo "⚠️  未找到.cxx构建目录"
fi

# 检查关键源文件
echo "✅ 源文件检查:"
[ -f "src/main/cpp/opus_player.cpp" ] && echo "  - opus_player.cpp ✅" || echo "  - opus_player.cpp ❌"
[ -f "src/main/cpp/audio_decoder.cpp" ] && echo "  - audio_decoder.cpp ✅" || echo "  - audio_decoder.cpp ❌"
[ -f "src/main/cpp/napi_init.cpp" ] && echo "  - napi_init.cpp ✅" || echo "  - napi_init.cpp ❌"
[ -f "src/main/cpp/CMakeLists.txt" ] && echo "  - CMakeLists.txt ✅" || echo "  - CMakeLists.txt ❌"

echo "=== 验证完成 ==="
