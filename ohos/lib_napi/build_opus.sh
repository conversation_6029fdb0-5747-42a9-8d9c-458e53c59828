#!/bin/bash

# Opus音频库构建脚本 for HarmonyOS
# 此脚本用于编译opus-1.5.2库并生成NAPI模块

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的工具
check_tools() {
    log_info "检查构建工具..."
    
    if ! command -v cmake &> /dev/null; then
        log_error "CMake未找到，请安装CMake"
        exit 1
    fi
    
    if ! command -v make &> /dev/null; then
        log_error "Make未找到，请安装Make"
        exit 1
    fi
    
    log_success "构建工具检查完成"
}

# 设置环境变量
setup_environment() {
    log_info "设置构建环境..."
    
    # 获取脚本所在目录
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    PROJECT_ROOT="$SCRIPT_DIR"
    OPUS_DIR="$PROJECT_ROOT/opus-1.5.2"
    BUILD_DIR="$PROJECT_ROOT/build"
    CPP_DIR="$PROJECT_ROOT/src/main/cpp"
    
    # 检查opus源码目录
    if [ ! -d "$OPUS_DIR" ]; then
        log_error "Opus源码目录不存在: $OPUS_DIR"
        exit 1
    fi
    
    # 创建构建目录
    mkdir -p "$BUILD_DIR"
    
    log_success "环境设置完成"
}

# 生成config.h文件
generate_config() {
    log_info "生成Opus配置文件..."
    
    cat > "$OPUS_DIR/config.h" << 'EOF'
#ifndef CONFIG_H
#define CONFIG_H

/* Define to 1 if you have the <dlfcn.h> header file. */
#define HAVE_DLFCN_H 1

/* Define to 1 if you have the <inttypes.h> header file. */
#define HAVE_INTTYPES_H 1

/* Define to 1 if you have the `lrint' function. */
#define HAVE_LRINT 1

/* Define to 1 if you have the `lrintf' function. */
#define HAVE_LRINTF 1

/* Define to 1 if you have the <memory.h> header file. */
#define HAVE_MEMORY_H 1

/* Define to 1 if you have the <stdint.h> header file. */
#define HAVE_STDINT_H 1

/* Define to 1 if you have the <stdlib.h> header file. */
#define HAVE_STDLIB_H 1

/* Define to 1 if you have the <strings.h> header file. */
#define HAVE_STRINGS_H 1

/* Define to 1 if you have the <string.h> header file. */
#define HAVE_STRING_H 1

/* Define to 1 if you have the <sys/stat.h> header file. */
#define HAVE_SYS_STAT_H 1

/* Define to 1 if you have the <sys/types.h> header file. */
#define HAVE_SYS_TYPES_H 1

/* Define to 1 if you have the <unistd.h> header file. */
#define HAVE_UNISTD_H 1

/* Define to the sub-directory where libtool stores uninstalled libraries. */
#define LT_OBJDIR ".libs/"

/* Name of package */
#define PACKAGE "opus"

/* Define to the address where bug reports for this package should be sent. */
#define PACKAGE_BUGREPORT "<EMAIL>"

/* Define to the full name of this package. */
#define PACKAGE_NAME "opus"

/* Define to the full name and version of this package. */
#define PACKAGE_STRING "opus 1.5.2"

/* Define to the one symbol short name of this package. */
#define PACKAGE_TARNAME "opus"

/* Define to the home page for this package. */
#define PACKAGE_URL ""

/* Define to the version of this package. */
#define PACKAGE_VERSION "1.5.2"

/* Define to 1 if you have the ANSI C header files. */
#define STDC_HEADERS 1

/* Enable extensions on AIX 3, Interix.  */
#ifndef _ALL_SOURCE
# define _ALL_SOURCE 1
#endif

/* Enable GNU extensions on systems that have them.  */
#ifndef _GNU_SOURCE
# define _GNU_SOURCE 1
#endif

/* Enable threading extensions on Solaris.  */
#ifndef _POSIX_PTHREAD_SEMANTICS
# define _POSIX_PTHREAD_SEMANTICS 1
#endif

/* Enable extensions on HP NonStop.  */
#ifndef _TANDEM_SOURCE
# define _TANDEM_SOURCE 1
#endif

/* Enable general extensions on Solaris.  */
#ifndef __EXTENSIONS__
# define __EXTENSIONS__ 1
#endif

/* Version number of package */
#define VERSION "1.5.2"

/* Define to 1 if on MINIX. */
/* #undef _MINIX */

/* Define to 2 if the system does not provide POSIX.1 features except with
   this defined. */
/* #undef _POSIX_1_SOURCE */

/* Define to 1 if you need to in order for `stat' and other things to work. */
/* #undef _POSIX_SOURCE */

/* Use C99 variable-length arrays */
#define VAR_ARRAYS 1

/* Use alloca() */
/* #define USE_ALLOCA 1 */

/* Define OPUS_BUILD if building Opus */
#define OPUS_BUILD 1

/* Use fixed-point arithmetic */
#define FIXED_POINT 1

#endif /* CONFIG_H */
EOF
    
    log_success "配置文件生成完成"
}

# 构建项目
build_project() {
    log_info "开始构建项目..."
    
    cd "$BUILD_DIR"
    
    # 运行CMake配置
    log_info "运行CMake配置..."
    cmake "$CPP_DIR" \
        -DCMAKE_BUILD_TYPE=Release \
        -DCMAKE_VERBOSE_MAKEFILE=ON
    
    # 编译项目
    log_info "编译项目..."
    make -j$(nproc)
    
    log_success "项目构建完成"
}

# 检查构建结果
check_build_result() {
    log_info "检查构建结果..."

    # 在不同平台上库文件扩展名不同
    if [[ "$OSTYPE" == "darwin"* ]]; then
        LIB_FILE="$BUILD_DIR/liblib_napi.dylib"
    else
        LIB_FILE="$BUILD_DIR/liblib_napi.so"
    fi

    if [ -f "$LIB_FILE" ]; then
        log_success "NAPI库构建成功: $LIB_FILE"

        # 显示库信息
        log_info "库文件信息:"
        ls -lh "$LIB_FILE"

        # 检查库依赖
        if command -v ldd &> /dev/null; then
            log_info "库依赖信息:"
            ldd "$LIB_FILE" || true
        elif command -v otool &> /dev/null; then
            log_info "库依赖信息 (macOS):"
            otool -L "$LIB_FILE" || true
        fi

        # 检查Opus静态库
        OPUS_LIB="$BUILD_DIR/libopus.a"
        if [ -f "$OPUS_LIB" ]; then
            log_success "Opus静态库构建成功: $OPUS_LIB"
            ls -lh "$OPUS_LIB"
        fi

        return 0
    else
        log_error "NAPI库构建失败"
        return 1
    fi
}

# 清理构建
clean_build() {
    log_info "清理构建目录..."
    
    if [ -d "$BUILD_DIR" ]; then
        rm -rf "$BUILD_DIR"
        log_success "构建目录已清理"
    fi
    
    if [ -f "$OPUS_DIR/config.h" ]; then
        rm -f "$OPUS_DIR/config.h"
        log_success "配置文件已清理"
    fi
}

# 显示帮助信息
show_help() {
    echo "Opus音频库构建脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  build     构建项目 (默认)"
    echo "  clean     清理构建文件"
    echo "  rebuild   清理后重新构建"
    echo "  help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 build    # 构建项目"
    echo "  $0 clean    # 清理构建文件"
    echo "  $0 rebuild  # 重新构建"
}

# 主函数
main() {
    local action="${1:-build}"
    
    case "$action" in
        "build")
            check_tools
            setup_environment
            generate_config
            build_project
            check_build_result
            ;;
        "clean")
            setup_environment
            clean_build
            ;;
        "rebuild")
            setup_environment
            clean_build
            check_tools
            generate_config
            build_project
            check_build_result
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知选项: $action"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
