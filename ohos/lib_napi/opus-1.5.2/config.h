#ifndef CONFIG_H
#define CONFIG_H

/* HarmonyOS specific configuration for Opus */

/* Define to 1 if you have the <inttypes.h> header file. */
#define HAVE_INTTYPES_H 1

/* Define to 1 if you have the `lrint' function. */
#define HAVE_LRINT 1

/* Define to 1 if you have the `lrintf' function. */
#define HAVE_LRINTF 1

/* Define to 1 if you have the <stdint.h> header file. */
#define HAVE_STDINT_H 1

/* Define to 1 if you have the <stdlib.h> header file. */
#define HAVE_STDLIB_H 1

/* Define to 1 if you have the <string.h> header file. */
#define HAVE_STRING_H 1

/* Define to 1 if you have the <unistd.h> header file. */
#define HAVE_UNISTD_H 1

/* Name of package */
#define PACKAGE "opus"

/* Define to the version of this package. */
#define PACKAGE_VERSION "1.5.2"

/* Version number of package */
#define VERSION "1.5.2"

/* Use C99 variable-length arrays */
#define VAR_ARRAYS 1

/* Use alloca() */
#define USE_ALLOCA 1

/* Define OPUS_BUILD if building Opus */
#define OPUS_BUILD 1

/* Use fixed-point arithmetic for HarmonyOS */
#define FIXED_POINT 1

/* HarmonyOS MUSL compatibility */
#define __MUSL__ 1

#endif /* CONFIG_H */
