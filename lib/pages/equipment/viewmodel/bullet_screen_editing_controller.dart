import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:gs_fit_flutter/equipment_module/channel/jl_equipment.dart';
import 'package:gs_fit_flutter/equipment_module/constant/health_constant.dart';
import 'package:gs_fit_flutter/equipment_module/constant/sp_constant.dart';
import 'package:gs_fit_flutter/utils/click_util.dart';
import 'package:gs_fit_flutter/utils/text_to_image_util.dart';
import 'package:shared_preferences/shared_preferences.dart';

class BulletScreenEditingController extends GetxController {
  // 弹幕文本内容
  final bulletText = ''.obs;
  var uploadPic = false;

  // 跑马灯控制
  final isMarqueeRunning = true.obs; // 默认开启跑马灯效果
  final isLeftToRight = false.obs; // 默认从右向左

  late SharedPreferences prefs;

  //是否支持弹幕图片
  final barragePic = 0.obs;

  final direction = true.obs;

  final key = GlobalKey(debugLabel: 'BulletScreenEditingPage');

  // 字号选项
  final List<String> fontSizes = ['小号', '中号', '大号', '超大号'];
  final selectedFontSize = 1.obs; // 默认选中中号 (索引1)

  // 字体颜色选项
  final List<Color> fontColors = [
    Colors.white,
    Colors.black,
    Colors.red,
    Colors.orange,
    Colors.lime,
    Colors.cyan,
    Colors.blue,
    Colors.indigo,
  ];
  final selectedFontColor = 0.obs; // 默认选中白色 (索引0)

  // 背景颜色选项
  final List<Color> bgColors = [
    Colors.black,
    Colors.white,
    Colors.red,
    Colors.orange,
    Colors.lime,
    Colors.cyan,
    Colors.blue,
    Colors.indigo,
  ];
  final selectedBgColor = 0.obs; // 默认选中靛蓝色 (索引7)

  // 预览图中显示的文字
  String get previewText => bulletText.value.isEmpty ? '你好' : bulletText.value;

  // 获取当前选中的字体大小
  double getFontSize() {
    switch (selectedFontSize.value) {
      case 0:
        return 16.0; // 小号 90
      case 1:
        return 24.0; // 中号 110
      case 2:
        return 32.0; // 大号 130
      case 3:
        return 40.0; // 超大号 180
      default:
        return 24.0;
    }
  }

  uploadFontSize() {
    switch (selectedFontSize.value) {
      case 0:
        return 90.0; // 小号 90
      case 1:
        return 110.0; // 中号 110
      case 2:
        return 130.0; // 大号 130
      case 3:
        return 180.0; // 超大号 180
      default:
        return 110.0;
    }
  }

  @override
  void onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    var sendCustomCommand = await JLEquipment.instance.sendCustomCommand(HealthConstant.COMMAND_BARRAGE);
    if (sendCustomCommand?.isNotEmpty == true) {
      List<int> data = sendCustomCommand!.split(',').map((e) => int.tryParse(e) ?? 0).toList();
      if (data.length < 4 || data[3] == 0) {
        barragePic.value = 3;
        EasyLoading.showToast('设备不支持发送弹幕');
        Get.back();
      }
      uploadPic = false;
    }
  }

  // 切换跑马灯效果
  void toggleMarquee(bool value) {
    isMarqueeRunning.value = value;
  }

  // 切换跑马灯方向
  void toggleDirection(bool value) {
    isLeftToRight.value = value;
  }

  // 更新弹幕文本
  void updateBulletText(String text) {
    if (text.isEmpty) return;
    if (bulletText.value.isEmpty || bulletText.value != text && ClickUtil.isDoubleClick(intervalMilliseconds: 3000)) {
      captureTextFieldToImage(text: text);
    }
    bulletText.value = text;
  }

  // 选择字号
  void selectFontSize(int index) {
    selectedFontSize.value = index;
  }

  // 选择字体颜色
  void selectFontColor(int index) {
    selectedFontColor.value = index;
  }

  // 选择背景颜色
  void selectBgColor(int index) {
    selectedBgColor.value = index;
  }

  void selectDirection() {
    direction.value = !direction.value;
  }

  captureTextFieldToImage({String text = ''}) async {
    try {
      uploadPic = true;

      final Uint8List bytes = await TextToImageUtil.textToImageWithCanvas(
        text: text,
        fontSize: uploadFontSize(),
        textColor: Colors.black,
        backgroundColor: Colors.white,
      );

      if (kDebugMode) {
        print('uint8List  image: length ${bytes.length}===text=$text===fontSize==${uploadFontSize()}');
      }
      if (bytes.isNotEmpty) JLEquipment.instance.sendCustomCommand(-1, cmd: bytes.join(','));

      /*// 获取RenderRepaintBoundary
      RenderRepaintBoundary boundary = key.currentContext!.findRenderObject() as RenderRepaintBoundary;

      // 捕获为图片，pixelRatio可调整以提高图片质量
      ui.Image image = await boundary.toImage(pixelRatio: 3.0);

      // 转换为PNG格式的ByteData
      ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      if (byteData == null) return '';

      // 转换为Uint8List
      Uint8List pngBytes = byteData.buffer.asUint8List();
      if (kDebugMode) {
        print('uint8List  image: length ${pngBytes.length}');
      }

      if (pngBytes.isNotEmpty) JLEquipment.instance.sendCustomCommand(-1, cmd: pngBytes.join(','));*/

      // 获取应用文档目录
      // final directory = (await getApplicationDocumentsDirectory()).path;

      // 创建文件名
      // String filePath = '$directory/text_image_${DateTime.now().millisecondsSinceEpoch}.png';

      // // 保存文件
      // File imgFile = File(filePath);
      // await imgFile.writeAsBytes(pngBytes);

      // return filePath;
    } catch (e) {
      if (kDebugMode) {
        print('Error capturing TextField as image: $e');
      }
    }
  }

  exitPage() {
    List<int> cmd = [];
    cmd.add(HealthConstant.COMMAND_HEADER);
    cmd.add(HealthConstant.COMMAND_BARRAGE);
    cmd.add(HealthConstant.COMMAND_NOT_REMIND);
    JLEquipment.instance.sendCustomCommand(22, cmd: cmd.join(','));
  }

  captureTextFieldAsImage() async {
    List<int> cmd = [];
    cmd.add(HealthConstant.COMMAND_HEADER);
    cmd.add(HealthConstant.COMMAND_BARRAGE);
    cmd.add(uploadPic ? HealthConstant.COMMAND_POWER : HealthConstant.COMMAND_DRINK_REMIND);
    cmd.add(fontColors[selectedFontColor.value].red);
    cmd.add(fontColors[selectedFontColor.value].green);
    cmd.add(fontColors[selectedFontColor.value].blue);
    cmd.add(bgColors[selectedBgColor.value].red);
    cmd.add(bgColors[selectedBgColor.value].green);
    cmd.add(bgColors[selectedBgColor.value].blue);
    cmd.add(direction.value ? HealthConstant.COMMAND_POWERHEAD : HealthConstant.COMMAND_POWER);
    cmd.add(selectedFontSize.value + 1);
    cmd.add(HealthConstant.COMMAND_NOT_REMIND);
    uploadPic = false;
    var command = await JLEquipment.instance.sendCustomCommand(21, cmd: cmd.join(','));
    print('接收弹幕数据: $command');
  }

  // 发送弹幕
  void sendBulletScreen() {
    /*if (bulletText.value.isEmpty) {
      EasyLoading.showToast('请输入弹幕内容');
      return;
    }*/

    if (barragePic.value == 3) {
      EasyLoading.showToast('设备不支持发送弹幕');
      return;
    }

    if (fontColors[selectedFontColor.value] == bgColors[selectedBgColor.value]) {
      EasyLoading.showToast('字体颜色和背景颜色不能相同');
      return;
    }

    if (previewText == '你好') {
      captureTextFieldToImage(text: previewText);
    }

    final bulletData = {
      'text': bulletText.value,
      'fontSize': getFontSize(),
      'fontColor': fontColors[selectedFontColor.value],
      'bgColor': bgColors[selectedBgColor.value],
      'isMarquee': isMarqueeRunning.value,
      'isLeftToRight': isLeftToRight.value,
    };

    // TODO: 处理发送逻辑，可能需要调用API或通过蓝牙发送到设备
    print('发送弹幕: $bulletData');

    // 发送成功后返回上一页
    // Get.back(result: bulletData);

    // captureTextFieldToImage();
    captureTextFieldAsImage();
  }
}
