import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:gs_fit_flutter/equipment_module/constant/health_constant.dart';

class SpConstant {
  static const FILEPATH = '/data/storage/el2/base/haps/entry/files';
  static const FILEPATH_P_TEMP = '/data/storage/el2/base/haps/entry/temp';
  static const FILEPATH_TEMP = '/tempDir';
  static const WATCH_DOWNLOADER_FILEPATH = '/watch';
  static const WATCH_DOWNLOADER_CUSTOM_FILEPATH = '/custom'; //自定义表盘路径
  static const JPG = '.jpg';

  static const PAGESIZE = 20;
  static const watchLogo = 'watchLogo';
  static const firmwareType = 'firmwareType';
  static const videoHelpUrl = 'videoHelpUrl'; //视频指导地址
  static const isShowAllSport = 'isShowAllSport'; //手表更多运动 0：不展示更多运动； 1：展示所有更多运动； 2.展示三星对应pid更多运动；
  static const dialResolutionType = 'dialResolutionType'; //表盘分辨率类型  0： width<height ; 1： width=height ; 2： width>height
  static const LANGUAGE_ZH = 'zh';
  static const SP_BARRAGE = 'barrage'; //是否支持弹幕
  static const SP_FIRMWARERESTORATIONPWD = 'firmwareRestorationPwd'; //固件修复密码
  static const SP_DEVICEOTAFIRMWARE = 'deviceOTAFirmware'; //最新固件
  static const SP_WOMEN_HEALTH_FIRST_SET = 'women_health_first_set'; //是否第一次进入女性健康页面
  static const SP_HASAIDIAL = 'hasAiDial'; //ai表盘
  static const SP_VOICEWAKEUP = 'voiceWakeUp'; //语音唤醒
  static const SP_ORIGINAIIMGLIST = 'originAiImgList'; //ai表盘生成图片的原始路径集合
  static const SP_PRESENTSTYLE = 'presentStyle'; //当前ai表盘风格
  static const SP_REALFIRMWAREPID = 'realFirmwarePid'; //当前ai表盘风格
  static const SP_DIALPLATEWORKLUNCHTIME = 'dialPlateworkLunchTime'; //工作时间手表数据
  static const SP_DIALPLATEWORKLUNCHTIME_WORKDAYS = 'dialPlateworkLunchTimeworkDays'; //工作时间手表数据

  static String getVersionName(String? versionName) {
    if (versionName?.isEmpty == true) {
      return "0000";
    } else {
      return versionName!.replaceAll("V_", "").replaceAll(".", "");
    }
  }

  static Future<String> getAppDownloadDialPlateDir({String? dir,String? pDir}) async {
    var downloadDir = '${pDir ?? SpConstant.FILEPATH}${dir ?? SpConstant.WATCH_DOWNLOADER_CUSTOM_FILEPATH}';
    await Directory(downloadDir).create(recursive: true);
    return downloadDir;
  }

  static Future<String?> saveFilePrepare(int audioType) async {
    try {
      String fileName = '${DateTime.now().millisecond}';
      switch (audioType) {
        case 0:
          return getAppDownloadDialPlateDir(dir: '${HealthConstant.DIR_RECORD}/record-$fileName.opus');
        case 1:
          return getAppDownloadDialPlateDir(dir: '${HealthConstant.DIR_AI_RECORD}/record-$fileName.opus');
        case 2:
          return getAppDownloadDialPlateDir(dir: '${HealthConstant.DIR_AI_RECORD}/record-ai-img-$fileName.opus');
        case 3:
          return getAppDownloadDialPlateDir(dir: '${HealthConstant.DIR_AI_RECORD}/record-ai-assistant-$fileName.opus');
        case 4:
          return getAppDownloadDialPlateDir(dir: '${HealthConstant.DIR_AI_TRANSLATE}/record-$fileName.opus');
        case 5:
          return getAppDownloadDialPlateDir(dir: '${HealthConstant.DIR_TRANSLATE_VOICE}/record-$fileName.opus');
        case 6:
          /*if (SpMap.getAssistantAiType() == 1) {
            outputFilePath = HealthUtil.createFilePath(MainApplication.getMainApplication(), HealthConstant.DIR_AI_RECORD) + "/record-" + fileName + ".opus";
          } else if (SpMap.getAssistantAiType() == 2) {
            outputFilePath = HealthUtil.createFilePath(MainApplication.getMainApplication(), HealthConstant.DIR_AI_RECORD) + "/record-ai-assistant-" + fileName + ".opus";
          } else {
            outputFilePath = HealthUtil.createFilePath(MainApplication.getMainApplication(), HealthConstant.DIR_ASSISTANT) + "/record-" + fileName + ".opus";
          }*/
          break;
        case 7:
          return getAppDownloadDialPlateDir(dir: '${HealthConstant.DIR_AI_RECORD}/record-deep-seek-$fileName.opus');
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return null;
  }
}
