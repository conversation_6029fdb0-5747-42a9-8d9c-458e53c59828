import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:gs_fit_flutter/common/app_config.dart';
import 'package:gs_fit_flutter/utils/apis/api_config.dart';
import 'package:gs_fit_flutter/utils/http/gs_http_error.dart';
import 'package:gs_fit_flutter/utils/http/gs_http_response.dart';
import 'package:gs_fit_flutter/utils/http/gs_http_rsa.dart';
import 'package:gs_fit_flutter/utils/http/gs_http_sign.dart';

const String APPLICATION_JSON = "application/json";
const String CONTENT_TYPE = "content-type";
const String ACCEPT = "accept";
const String AUTHORIZATION = "authorization";
const String DEFAULT_LANGUAGE = "zh";
const String TOKEN = "Bearer token";

/// 请求工具类
class GsHttp {
  static final GsHttp _instance = GsHttp._internal();

  factory GsHttp() => _instance;

  late Dio _dio;

  String get baseUrl {
    return _dio.options.baseUrl;
  }

  /// 单例初始
  GsHttp._internal() {
    // header 头
    Map<String, String> headers = {
      // CONTENT_TYPE: APPLICATION_JSON,
      // ACCEPT: APPLICATION_JSON,
      //AUTHORIZATION: TOKEN,
      //DEFAULT_LANGUAGE: DEFAULT_LANGUAGE,
    };
    // 初始选项
    var options = BaseOptions(
      // baseUrl: ApiConfig.ApiOnlineUrl,
      baseUrl: ApiConfig.ApiDevUrl,
      headers: headers,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
    );

    // 初始 dio
    _dio = Dio(options);
    // 拦截器
    _dio.interceptors.add(RequestInterceptors());
  }

  /// get 请求
  Future<Response> get(
    String url, {
    Map<String, dynamic>? params,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    Options requestOptions = options ?? Options();
    Response response = await _dio.get(
      url,
      queryParameters: params,
      options: requestOptions,
      cancelToken: cancelToken,
    );
    return response;
  }

  download(
    String path,
    String savePath, {
    dynamic data,
    bool? sign = true,
    ProgressCallback? onReceiveProgress,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      var options = BaseOptions(
        baseUrl: ApiConfig.ApiOnlineUrl,
        connectTimeout: const Duration(seconds: 30),
        // receiveTimeout: const Duration(seconds: 30),
        // sendTimeout: const Duration(seconds: 30),
      );

      // 初始 dio
      Dio downloadDio = Dio(options);

      // 拦截器
      downloadDio.interceptors.add(RequestInterceptors());

      // 加密请求参数
      if (sign == true) {
        data = GsHttpSign.packParams(jsonEncode(data));
      }
      await downloadDio.download(
        path,
        savePath,
        onReceiveProgress: onReceiveProgress,
        data: data,
        options: Options(method: 'POST'),
        cancelToken: cancelToken,
      );
    } catch (e) {
      debugPrint('下载文件失败: $e');
    }
  }

  Future<GsHttpResponse<T>> post<T>(
    String path, {
    dynamic data,
    bool? sign = true,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    required T Function(dynamic json) fromJsonT,
  }) async {
    try {
      // 加密请求参数
      if (sign == true) {
        data = GsHttpSign.packParams(jsonEncode(data));
      }
      final response = await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
      // 解密data
      if (response.data["data"] != null) {
        final dynamic resJson =
            await GsHttpSign.convertResponseBody(response.data);
        response.data["data"] = resJson;
        if (kDebugMode) {
          print("response.data = ${response.data}");
        }
      }
      GsHttpResponse<T> res = GsHttpResponse.fromJson(response.data, fromJsonT);
      return res;
    } catch (e) {
      return GsHttpResponse(
        code: -1,
        message: e.toString(),
      );
    }
  }

// /// put 请求
// Future<Response> put(
//   String url, {
//   dynamic data,
//   Options? options,
//   CancelToken? cancelToken,
// }) async {
//   var requestOptions = options ?? Options();
//   Response response = await _dio.put(
//     url,
//     data: data ?? {},
//     options: requestOptions,
//     cancelToken: cancelToken,
//   );
//   return response;
// }

// /// delete 请求
// Future<Response> delete(
//   String url, {
//   dynamic data,
//   Options? options,
//   CancelToken? cancelToken,
// }) async {
//   var requestOptions = options ?? Options();
//   Response response = await _dio.delete(
//     url,
//     data: data ?? {},
//     options: requestOptions,
//     cancelToken: cancelToken,
//   );
//   return response;
// }
}

/// 拦截
class RequestInterceptors extends Interceptor {
  //

  /// 发送请求
  /// 添加公共参数，或者对参数进行加密
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    if (AppConfig().loginState) {
      options.headers["authorization"] = AppConfig().user.authorization;
      options.headers["random"] = AppConfig().user.userId.toString();
    }

    // 时间戳加密
    int timestamp = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    String timestampPar = "{\"timeStamp\":\"" +
        timestamp.toString() +
        "\",\"saltr\":\"goijdgsepi\"}";
    String rsaParams = RSAUtils.encryptHE(timestampPar);
    // 时间戳加密参数
    options.headers["empower"] = rsaParams;
    options.headers["phone_system"] = "1";
    options.headers["phone_brand"] = "1";

    print("未加密参数为：$timestampPar, 加密后为：$rsaParams, headers: $options.headers");
    print("请求url:${options.uri}");
    print("请求参数:${options.queryParameters}");
    print("请求头:${options.headers}");
    print("请求数据:${options.data}");
    return handler.next(options);
  }

  /// 响应
  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (kDebugMode) {
      // print(
      //     '┌------------------------------------------------------------------------------');
      // print('| Response URL: ${response.requestOptions.uri}');
      // print('| Response Status Code: ${response.statusCode}');
      // // print('| Response Headers:');
      // // response.headers.forEach((name, values) {
      // //   print('|   $name: ${values.join(',')}');
      // // });
      // print('| Response Data:');
      // print('|   ${response.data}');
      // print(
      //     '└------------------------------------------------------------------------------');
    }
    if (response.data is Map) {
      Map<String, dynamic> data = response.data;
      // tonken失效
      if (data["code"] == 422) {
        // 登录过期
        AppConfig().logoutUser();
      }
    }
    super.onResponse(response, handler);
    // 200 请求成功, 201 添加成功
    // if (response.statusCode != 200 && response.statusCode != 201) {
    //   handler.reject(
    //     DioException(
    //       requestOptions: response.requestOptions,
    //       response: response,
    //       type: DioExceptionType.badResponse,
    //     ),
    //     true,
    //   );
    // } else {
    //   // 解析加密数据包

    //   handler.next(response);
    // }
  }

  // // 退出并重新登录
  // Future<void> _errorNoAuthLogout() async {
  //   await UserService.to.logout();
  //   IMService.to.logout();
  //   Get.toNamed(RouteNames.systemLogin);
  // }

  /// 错误
  @override
  Future<void> onError(
      DioException err, ErrorInterceptorHandler handler) async {
    final exception = HttpException(err.message ?? "error message");
    switch (err.type) {
      case DioExceptionType.badResponse: // 服务端自定义错误体处理
        {
          final response = err.response;
          final errorMessage = GsHttpErrorMessageModel.fromJson(response?.data);
          switch (errorMessage.statusCode) {
            // 401 未登录
            case 401:
              // 注销 并跳转到登录页面
              // _errorNoAuthLogout();
              break;
            case 404:
              break;
            case 500:
              break;
            case 502:
              break;
            default:
              break;
          }
          // 显示错误信息
          // if(errorMessage.message != null){
          //   Loading.error(errorMessage.message);
          // }
        }
        break;
      case DioExceptionType.unknown:
        break;
      case DioExceptionType.cancel:
        break;
      case DioExceptionType.connectionTimeout:
        break;
      default:
        break;
    }
    DioException errNext = err.copyWith(
      error: exception,
    );
    handler.next(errNext);
  }
}
